#!/usr/bin/env just --justfile

default:
    @just -l

pkg:
    rm -rf ./pkg
    git clone --depth 1 https://gitlab.minum.cloud/innovationteam/pypkg.git ./pkg
    cd pkg && rm -rf .git
    git add pkg

gen-api:
    @cd aiapi && buf format -w && buf generate

proto file:
    # example just proto api/health/health.proto
    python3 -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. --pyi_out=. {{file}}
    just gen-api

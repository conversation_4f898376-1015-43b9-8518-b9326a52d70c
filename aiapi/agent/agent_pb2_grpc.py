# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from aiapi.agent import agent_pb2 as aiapi_dot_agent_dot_agent__pb2

GRPC_GENERATED_VERSION = '1.67.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in aiapi/agent/agent_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AgentStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CallAgent = channel.unary_stream(
                '/aiapi.agent.Agent/CallAgent',
                request_serializer=aiapi_dot_agent_dot_agent__pb2.CallAgentRequest.SerializeToString,
                response_deserializer=aiapi_dot_agent_dot_agent__pb2.CallAgentReply.FromString,
                _registered_method=True)
        self.CallAgentTest = channel.unary_unary(
                '/aiapi.agent.Agent/CallAgentTest',
                request_serializer=aiapi_dot_agent_dot_agent__pb2.CallAgentTestRequest.SerializeToString,
                response_deserializer=aiapi_dot_agent_dot_agent__pb2.CallAgentTestReply.FromString,
                _registered_method=True)
        self.ContractReview = channel.unary_unary(
                '/aiapi.agent.Agent/ContractReview',
                request_serializer=aiapi_dot_agent_dot_agent__pb2.ContractReviewRequest.SerializeToString,
                response_deserializer=aiapi_dot_agent_dot_agent__pb2.ContractReviewReply.FromString,
                _registered_method=True)
        self.QuestionSecurityCheck = channel.unary_unary(
                '/aiapi.agent.Agent/QuestionSecurityCheck',
                request_serializer=aiapi_dot_agent_dot_agent__pb2.QuestionSecurityCheckRequest.SerializeToString,
                response_deserializer=aiapi_dot_agent_dot_agent__pb2.QuestionSecurityCheckReply.FromString,
                _registered_method=True)
        self.CheckAndSaveQuestionSemanticCache = channel.unary_unary(
                '/aiapi.agent.Agent/CheckAndSaveQuestionSemanticCache',
                request_serializer=aiapi_dot_agent_dot_agent__pb2.CheckAndSaveQuestionSemanticCacheRequest.SerializeToString,
                response_deserializer=aiapi_dot_agent_dot_agent__pb2.CheckAndSaveQuestionSemanticCacheReply.FromString,
                _registered_method=True)


class AgentServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CallAgent(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CallAgentTest(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContractReview(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QuestionSecurityCheck(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckAndSaveQuestionSemanticCache(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AgentServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CallAgent': grpc.unary_stream_rpc_method_handler(
                    servicer.CallAgent,
                    request_deserializer=aiapi_dot_agent_dot_agent__pb2.CallAgentRequest.FromString,
                    response_serializer=aiapi_dot_agent_dot_agent__pb2.CallAgentReply.SerializeToString,
            ),
            'CallAgentTest': grpc.unary_unary_rpc_method_handler(
                    servicer.CallAgentTest,
                    request_deserializer=aiapi_dot_agent_dot_agent__pb2.CallAgentTestRequest.FromString,
                    response_serializer=aiapi_dot_agent_dot_agent__pb2.CallAgentTestReply.SerializeToString,
            ),
            'ContractReview': grpc.unary_unary_rpc_method_handler(
                    servicer.ContractReview,
                    request_deserializer=aiapi_dot_agent_dot_agent__pb2.ContractReviewRequest.FromString,
                    response_serializer=aiapi_dot_agent_dot_agent__pb2.ContractReviewReply.SerializeToString,
            ),
            'QuestionSecurityCheck': grpc.unary_unary_rpc_method_handler(
                    servicer.QuestionSecurityCheck,
                    request_deserializer=aiapi_dot_agent_dot_agent__pb2.QuestionSecurityCheckRequest.FromString,
                    response_serializer=aiapi_dot_agent_dot_agent__pb2.QuestionSecurityCheckReply.SerializeToString,
            ),
            'CheckAndSaveQuestionSemanticCache': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckAndSaveQuestionSemanticCache,
                    request_deserializer=aiapi_dot_agent_dot_agent__pb2.CheckAndSaveQuestionSemanticCacheRequest.FromString,
                    response_serializer=aiapi_dot_agent_dot_agent__pb2.CheckAndSaveQuestionSemanticCacheReply.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'aiapi.agent.Agent', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('aiapi.agent.Agent', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class Agent(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CallAgent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/aiapi.agent.Agent/CallAgent',
            aiapi_dot_agent_dot_agent__pb2.CallAgentRequest.SerializeToString,
            aiapi_dot_agent_dot_agent__pb2.CallAgentReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CallAgentTest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.agent.Agent/CallAgentTest',
            aiapi_dot_agent_dot_agent__pb2.CallAgentTestRequest.SerializeToString,
            aiapi_dot_agent_dot_agent__pb2.CallAgentTestReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ContractReview(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.agent.Agent/ContractReview',
            aiapi_dot_agent_dot_agent__pb2.ContractReviewRequest.SerializeToString,
            aiapi_dot_agent_dot_agent__pb2.ContractReviewReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def QuestionSecurityCheck(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.agent.Agent/QuestionSecurityCheck',
            aiapi_dot_agent_dot_agent__pb2.QuestionSecurityCheckRequest.SerializeToString,
            aiapi_dot_agent_dot_agent__pb2.QuestionSecurityCheckReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckAndSaveQuestionSemanticCache(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.agent.Agent/CheckAndSaveQuestionSemanticCache',
            aiapi_dot_agent_dot_agent__pb2.CheckAndSaveQuestionSemanticCacheRequest.SerializeToString,
            aiapi_dot_agent_dot_agent__pb2.CheckAndSaveQuestionSemanticCacheReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: agent/agent.proto

package agent

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Agent_CallAgent_FullMethodName                         = "/aiapi.agent.Agent/CallAgent"
	Agent_CallAgentTest_FullMethodName                     = "/aiapi.agent.Agent/CallAgentTest"
	Agent_ContractReview_FullMethodName                    = "/aiapi.agent.Agent/ContractReview"
	Agent_QuestionSecurityCheck_FullMethodName             = "/aiapi.agent.Agent/QuestionSecurityCheck"
	Agent_CheckAndSaveQuestionSemanticCache_FullMethodName = "/aiapi.agent.Agent/CheckAndSaveQuestionSemanticCache"
)

// AgentClient is the client API for Agent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgentClient interface {
	CallAgent(ctx context.Context, in *CallAgentRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[CallAgentReply], error)
	CallAgentTest(ctx context.Context, in *CallAgentTestRequest, opts ...grpc.CallOption) (*CallAgentTestReply, error)
	ContractReview(ctx context.Context, in *ContractReviewRequest, opts ...grpc.CallOption) (*ContractReviewReply, error)
	QuestionSecurityCheck(ctx context.Context, in *QuestionSecurityCheckRequest, opts ...grpc.CallOption) (*QuestionSecurityCheckReply, error)
	CheckAndSaveQuestionSemanticCache(ctx context.Context, in *CheckAndSaveQuestionSemanticCacheRequest, opts ...grpc.CallOption) (*CheckAndSaveQuestionSemanticCacheReply, error)
}

type agentClient struct {
	cc grpc.ClientConnInterface
}

func NewAgentClient(cc grpc.ClientConnInterface) AgentClient {
	return &agentClient{cc}
}

func (c *agentClient) CallAgent(ctx context.Context, in *CallAgentRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[CallAgentReply], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Agent_ServiceDesc.Streams[0], Agent_CallAgent_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[CallAgentRequest, CallAgentReply]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Agent_CallAgentClient = grpc.ServerStreamingClient[CallAgentReply]

func (c *agentClient) CallAgentTest(ctx context.Context, in *CallAgentTestRequest, opts ...grpc.CallOption) (*CallAgentTestReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CallAgentTestReply)
	err := c.cc.Invoke(ctx, Agent_CallAgentTest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) ContractReview(ctx context.Context, in *ContractReviewRequest, opts ...grpc.CallOption) (*ContractReviewReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ContractReviewReply)
	err := c.cc.Invoke(ctx, Agent_ContractReview_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) QuestionSecurityCheck(ctx context.Context, in *QuestionSecurityCheckRequest, opts ...grpc.CallOption) (*QuestionSecurityCheckReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuestionSecurityCheckReply)
	err := c.cc.Invoke(ctx, Agent_QuestionSecurityCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) CheckAndSaveQuestionSemanticCache(ctx context.Context, in *CheckAndSaveQuestionSemanticCacheRequest, opts ...grpc.CallOption) (*CheckAndSaveQuestionSemanticCacheReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckAndSaveQuestionSemanticCacheReply)
	err := c.cc.Invoke(ctx, Agent_CheckAndSaveQuestionSemanticCache_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgentServer is the server API for Agent service.
// All implementations must embed UnimplementedAgentServer
// for forward compatibility.
type AgentServer interface {
	CallAgent(*CallAgentRequest, grpc.ServerStreamingServer[CallAgentReply]) error
	CallAgentTest(context.Context, *CallAgentTestRequest) (*CallAgentTestReply, error)
	ContractReview(context.Context, *ContractReviewRequest) (*ContractReviewReply, error)
	QuestionSecurityCheck(context.Context, *QuestionSecurityCheckRequest) (*QuestionSecurityCheckReply, error)
	CheckAndSaveQuestionSemanticCache(context.Context, *CheckAndSaveQuestionSemanticCacheRequest) (*CheckAndSaveQuestionSemanticCacheReply, error)
	mustEmbedUnimplementedAgentServer()
}

// UnimplementedAgentServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAgentServer struct{}

func (UnimplementedAgentServer) CallAgent(*CallAgentRequest, grpc.ServerStreamingServer[CallAgentReply]) error {
	return status.Errorf(codes.Unimplemented, "method CallAgent not implemented")
}
func (UnimplementedAgentServer) CallAgentTest(context.Context, *CallAgentTestRequest) (*CallAgentTestReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallAgentTest not implemented")
}
func (UnimplementedAgentServer) ContractReview(context.Context, *ContractReviewRequest) (*ContractReviewReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractReview not implemented")
}
func (UnimplementedAgentServer) QuestionSecurityCheck(context.Context, *QuestionSecurityCheckRequest) (*QuestionSecurityCheckReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionSecurityCheck not implemented")
}
func (UnimplementedAgentServer) CheckAndSaveQuestionSemanticCache(context.Context, *CheckAndSaveQuestionSemanticCacheRequest) (*CheckAndSaveQuestionSemanticCacheReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAndSaveQuestionSemanticCache not implemented")
}
func (UnimplementedAgentServer) mustEmbedUnimplementedAgentServer() {}
func (UnimplementedAgentServer) testEmbeddedByValue()               {}

// UnsafeAgentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgentServer will
// result in compilation errors.
type UnsafeAgentServer interface {
	mustEmbedUnimplementedAgentServer()
}

func RegisterAgentServer(s grpc.ServiceRegistrar, srv AgentServer) {
	// If the following call pancis, it indicates UnimplementedAgentServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Agent_ServiceDesc, srv)
}

func _Agent_CallAgent_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(CallAgentRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(AgentServer).CallAgent(m, &grpc.GenericServerStream[CallAgentRequest, CallAgentReply]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Agent_CallAgentServer = grpc.ServerStreamingServer[CallAgentReply]

func _Agent_CallAgentTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallAgentTestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).CallAgentTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_CallAgentTest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).CallAgentTest(ctx, req.(*CallAgentTestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_ContractReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).ContractReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_ContractReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).ContractReview(ctx, req.(*ContractReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_QuestionSecurityCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionSecurityCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).QuestionSecurityCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_QuestionSecurityCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).QuestionSecurityCheck(ctx, req.(*QuestionSecurityCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_CheckAndSaveQuestionSemanticCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAndSaveQuestionSemanticCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).CheckAndSaveQuestionSemanticCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_CheckAndSaveQuestionSemanticCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).CheckAndSaveQuestionSemanticCache(ctx, req.(*CheckAndSaveQuestionSemanticCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Agent_ServiceDesc is the grpc.ServiceDesc for Agent service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Agent_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "aiapi.agent.Agent",
	HandlerType: (*AgentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CallAgentTest",
			Handler:    _Agent_CallAgentTest_Handler,
		},
		{
			MethodName: "ContractReview",
			Handler:    _Agent_ContractReview_Handler,
		},
		{
			MethodName: "QuestionSecurityCheck",
			Handler:    _Agent_QuestionSecurityCheck_Handler,
		},
		{
			MethodName: "CheckAndSaveQuestionSemanticCache",
			Handler:    _Agent_CheckAndSaveQuestionSemanticCache_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CallAgent",
			Handler:       _Agent_CallAgent_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "agent/agent.proto",
}

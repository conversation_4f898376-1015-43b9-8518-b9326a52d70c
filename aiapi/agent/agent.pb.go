// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: agent/agent.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChatPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64    `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	EntityTag      string   `protobuf:"bytes,2,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag   string   `protobuf:"bytes,3,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Name           string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Size           int64    `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
	UserID         int64    `protobuf:"varint,6,opt,name=userID,proto3" json:"userID,omitempty"`
	MimeType       string   `protobuf:"bytes,7,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	Index          int64    `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex     int64    `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
	ChunkSize      int64    `protobuf:"varint,10,opt,name=chunkSize,proto3" json:"chunkSize,omitempty"`
	Content        string   `protobuf:"bytes,11,opt,name=content,proto3" json:"content,omitempty"`
	ImageKeys      []string `protobuf:"bytes,12,rep,name=imageKeys,proto3" json:"imageKeys,omitempty"`
	FullPath       string   `protobuf:"bytes,13,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
}

func (x *ChatPayload) Reset() {
	*x = ChatPayload{}
	mi := &file_agent_agent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatPayload) ProtoMessage() {}

func (x *ChatPayload) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatPayload.ProtoReflect.Descriptor instead.
func (*ChatPayload) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{0}
}

func (x *ChatPayload) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *ChatPayload) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *ChatPayload) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *ChatPayload) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatPayload) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ChatPayload) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *ChatPayload) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *ChatPayload) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *ChatPayload) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *ChatPayload) GetChunkSize() int64 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

func (x *ChatPayload) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ChatPayload) GetImageKeys() []string {
	if x != nil {
		return x.ImageKeys
	}
	return nil
}

func (x *ChatPayload) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

type CallAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query           string  `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	AgentID         int64   `protobuf:"varint,2,opt,name=agentID,proto3" json:"agentID,omitempty"`
	RoundID         int64   `protobuf:"varint,3,opt,name=roundID,proto3" json:"roundID,omitempty"`
	FileRelationIDs []int64 `protobuf:"varint,4,rep,packed,name=fileRelationIDs,proto3" json:"fileRelationIDs,omitempty"`
	ChatID          int64   `protobuf:"varint,5,opt,name=chatID,proto3" json:"chatID,omitempty"`
	IsMultiRound    bool    `protobuf:"varint,6,opt,name=isMultiRound,proto3" json:"isMultiRound,omitempty"`
	AiChatItemID    int64   `protobuf:"varint,7,opt,name=aiChatItemID,proto3" json:"aiChatItemID,omitempty"`
	InternetSearch  bool    `protobuf:"varint,8,opt,name=internetSearch,proto3" json:"internetSearch,omitempty"`
	Thinking        bool    `protobuf:"varint,9,opt,name=thinking,proto3" json:"thinking,omitempty"`
}

func (x *CallAgentRequest) Reset() {
	*x = CallAgentRequest{}
	mi := &file_agent_agent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallAgentRequest) ProtoMessage() {}

func (x *CallAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallAgentRequest.ProtoReflect.Descriptor instead.
func (*CallAgentRequest) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{1}
}

func (x *CallAgentRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *CallAgentRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *CallAgentRequest) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *CallAgentRequest) GetFileRelationIDs() []int64 {
	if x != nil {
		return x.FileRelationIDs
	}
	return nil
}

func (x *CallAgentRequest) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

func (x *CallAgentRequest) GetIsMultiRound() bool {
	if x != nil {
		return x.IsMultiRound
	}
	return false
}

func (x *CallAgentRequest) GetAiChatItemID() int64 {
	if x != nil {
		return x.AiChatItemID
	}
	return 0
}

func (x *CallAgentRequest) GetInternetSearch() bool {
	if x != nil {
		return x.InternetSearch
	}
	return false
}

func (x *CallAgentRequest) GetThinking() bool {
	if x != nil {
		return x.Thinking
	}
	return false
}

type Usage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PromptTokens     int64 `protobuf:"varint,1,opt,name=promptTokens,proto3" json:"promptTokens,omitempty"`
	CompletionTokens int64 `protobuf:"varint,2,opt,name=completionTokens,proto3" json:"completionTokens,omitempty"`
	TotalTokens      int64 `protobuf:"varint,3,opt,name=totalTokens,proto3" json:"totalTokens,omitempty"`
}

func (x *Usage) Reset() {
	*x = Usage{}
	mi := &file_agent_agent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Usage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Usage) ProtoMessage() {}

func (x *Usage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Usage.ProtoReflect.Descriptor instead.
func (*Usage) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{2}
}

func (x *Usage) GetPromptTokens() int64 {
	if x != nil {
		return x.PromptTokens
	}
	return 0
}

func (x *Usage) GetCompletionTokens() int64 {
	if x != nil {
		return x.CompletionTokens
	}
	return 0
}

func (x *Usage) GetTotalTokens() int64 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

type CallAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundID       int64          `protobuf:"varint,1,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Content       string         `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Status        int64          `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Payloads      []*ChatPayload `protobuf:"bytes,4,rep,name=payloads,proto3" json:"payloads,omitempty"`
	Type          int64          `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	Reason        string         `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	DebugContent  string         `protobuf:"bytes,7,opt,name=debugContent,proto3" json:"debugContent,omitempty"`
	Usage         *Usage         `protobuf:"bytes,8,opt,name=usage,proto3" json:"usage,omitempty"`
	CachePayloads string         `protobuf:"bytes,9,opt,name=cachePayloads,proto3" json:"cachePayloads,omitempty"`
	IsCached      bool           `protobuf:"varint,10,opt,name=isCached,proto3" json:"isCached,omitempty"`
}

func (x *CallAgentReply) Reset() {
	*x = CallAgentReply{}
	mi := &file_agent_agent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallAgentReply) ProtoMessage() {}

func (x *CallAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallAgentReply.ProtoReflect.Descriptor instead.
func (*CallAgentReply) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{3}
}

func (x *CallAgentReply) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *CallAgentReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CallAgentReply) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CallAgentReply) GetPayloads() []*ChatPayload {
	if x != nil {
		return x.Payloads
	}
	return nil
}

func (x *CallAgentReply) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CallAgentReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *CallAgentReply) GetDebugContent() string {
	if x != nil {
		return x.DebugContent
	}
	return ""
}

func (x *CallAgentReply) GetUsage() *Usage {
	if x != nil {
		return x.Usage
	}
	return nil
}

func (x *CallAgentReply) GetCachePayloads() string {
	if x != nil {
		return x.CachePayloads
	}
	return ""
}

func (x *CallAgentReply) GetIsCached() bool {
	if x != nil {
		return x.IsCached
	}
	return false
}

type CallAgentTestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query   string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	AgentID int64  `protobuf:"varint,2,opt,name=agentID,proto3" json:"agentID,omitempty"`
	RoundID int64  `protobuf:"varint,3,opt,name=roundID,proto3" json:"roundID,omitempty"`
}

func (x *CallAgentTestRequest) Reset() {
	*x = CallAgentTestRequest{}
	mi := &file_agent_agent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallAgentTestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallAgentTestRequest) ProtoMessage() {}

func (x *CallAgentTestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallAgentTestRequest.ProtoReflect.Descriptor instead.
func (*CallAgentTestRequest) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{4}
}

func (x *CallAgentTestRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *CallAgentTestRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *CallAgentTestRequest) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

type CallAgentTestReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content  string         `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Payloads []*ChatPayload `protobuf:"bytes,2,rep,name=payloads,proto3" json:"payloads,omitempty"`
}

func (x *CallAgentTestReply) Reset() {
	*x = CallAgentTestReply{}
	mi := &file_agent_agent_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallAgentTestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallAgentTestReply) ProtoMessage() {}

func (x *CallAgentTestReply) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallAgentTestReply.ProtoReflect.Descriptor instead.
func (*CallAgentTestReply) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{5}
}

func (x *CallAgentTestReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CallAgentTestReply) GetPayloads() []*ChatPayload {
	if x != nil {
		return x.Payloads
	}
	return nil
}

type CustomRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleTitle   string `protobuf:"bytes,1,opt,name=ruleTitle,proto3" json:"ruleTitle,omitempty"`
	RuleContent string `protobuf:"bytes,2,opt,name=ruleContent,proto3" json:"ruleContent,omitempty"`
}

func (x *CustomRule) Reset() {
	*x = CustomRule{}
	mi := &file_agent_agent_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomRule) ProtoMessage() {}

func (x *CustomRule) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomRule.ProtoReflect.Descriptor instead.
func (*CustomRule) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{6}
}

func (x *CustomRule) GetRuleTitle() string {
	if x != nil {
		return x.RuleTitle
	}
	return ""
}

func (x *CustomRule) GetRuleContent() string {
	if x != nil {
		return x.RuleContent
	}
	return ""
}

type ContractReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomRules []*CustomRule `protobuf:"bytes,1,rep,name=customRules,proto3" json:"customRules,omitempty"`
	PartyA      bool          `protobuf:"varint,2,opt,name=partyA,proto3" json:"partyA,omitempty"`
	FileContent string        `protobuf:"bytes,3,opt,name=fileContent,proto3" json:"fileContent,omitempty"`
}

func (x *ContractReviewRequest) Reset() {
	*x = ContractReviewRequest{}
	mi := &file_agent_agent_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractReviewRequest) ProtoMessage() {}

func (x *ContractReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractReviewRequest.ProtoReflect.Descriptor instead.
func (*ContractReviewRequest) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{7}
}

func (x *ContractReviewRequest) GetCustomRules() []*CustomRule {
	if x != nil {
		return x.CustomRules
	}
	return nil
}

func (x *ContractReviewRequest) GetPartyA() bool {
	if x != nil {
		return x.PartyA
	}
	return false
}

func (x *ContractReviewRequest) GetFileContent() string {
	if x != nil {
		return x.FileContent
	}
	return ""
}

type ContractReviewReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *ContractReviewReply) Reset() {
	*x = ContractReviewReply{}
	mi := &file_agent_agent_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractReviewReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractReviewReply) ProtoMessage() {}

func (x *ContractReviewReply) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractReviewReply.ProtoReflect.Descriptor instead.
func (*ContractReviewReply) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{8}
}

func (x *ContractReviewReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type QuestionSecurityCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question string   `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	Policies []string `protobuf:"bytes,2,rep,name=policies,proto3" json:"policies,omitempty"`
}

func (x *QuestionSecurityCheckRequest) Reset() {
	*x = QuestionSecurityCheckRequest{}
	mi := &file_agent_agent_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestionSecurityCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionSecurityCheckRequest) ProtoMessage() {}

func (x *QuestionSecurityCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionSecurityCheckRequest.ProtoReflect.Descriptor instead.
func (*QuestionSecurityCheckRequest) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{9}
}

func (x *QuestionSecurityCheckRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *QuestionSecurityCheckRequest) GetPolicies() []string {
	if x != nil {
		return x.Policies
	}
	return nil
}

type QuestionSecurityCheckReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hit bool `protobuf:"varint,1,opt,name=hit,proto3" json:"hit,omitempty"`
}

func (x *QuestionSecurityCheckReply) Reset() {
	*x = QuestionSecurityCheckReply{}
	mi := &file_agent_agent_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestionSecurityCheckReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionSecurityCheckReply) ProtoMessage() {}

func (x *QuestionSecurityCheckReply) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionSecurityCheckReply.ProtoReflect.Descriptor instead.
func (*QuestionSecurityCheckReply) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{10}
}

func (x *QuestionSecurityCheckReply) GetHit() bool {
	if x != nil {
		return x.Hit
	}
	return false
}

type CheckAndSaveQuestionSemanticCacheRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question string `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	Answer   string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
	RefFiles string `protobuf:"bytes,3,opt,name=refFiles,proto3" json:"refFiles,omitempty"`
	AgentID  int64  `protobuf:"varint,4,opt,name=agentID,proto3" json:"agentID,omitempty"`
}

func (x *CheckAndSaveQuestionSemanticCacheRequest) Reset() {
	*x = CheckAndSaveQuestionSemanticCacheRequest{}
	mi := &file_agent_agent_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAndSaveQuestionSemanticCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAndSaveQuestionSemanticCacheRequest) ProtoMessage() {}

func (x *CheckAndSaveQuestionSemanticCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAndSaveQuestionSemanticCacheRequest.ProtoReflect.Descriptor instead.
func (*CheckAndSaveQuestionSemanticCacheRequest) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{11}
}

func (x *CheckAndSaveQuestionSemanticCacheRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *CheckAndSaveQuestionSemanticCacheRequest) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *CheckAndSaveQuestionSemanticCacheRequest) GetRefFiles() string {
	if x != nil {
		return x.RefFiles
	}
	return ""
}

func (x *CheckAndSaveQuestionSemanticCacheRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

type CheckAndSaveQuestionSemanticCacheReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckAndSaveQuestionSemanticCacheReply) Reset() {
	*x = CheckAndSaveQuestionSemanticCacheReply{}
	mi := &file_agent_agent_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAndSaveQuestionSemanticCacheReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAndSaveQuestionSemanticCacheReply) ProtoMessage() {}

func (x *CheckAndSaveQuestionSemanticCacheReply) ProtoReflect() protoreflect.Message {
	mi := &file_agent_agent_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAndSaveQuestionSemanticCacheReply.ProtoReflect.Descriptor instead.
func (*CheckAndSaveQuestionSemanticCacheReply) Descriptor() ([]byte, []int) {
	return file_agent_agent_proto_rawDescGZIP(), []int{12}
}

var File_agent_agent_proto protoreflect.FileDescriptor

var file_agent_agent_proto_rawDesc = []byte{
	0x0a, 0x11, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x22, 0xfb, 0x02, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4b, 0x65, 0x79,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x22, 0xaa,
	0x02, 0x0a, 0x10, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x28, 0x0a,
	0x0f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49,
	0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x12,
	0x22, 0x0a, 0x0c, 0x69, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x52, 0x6f,
	0x75, 0x6e, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x69, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x69, 0x43, 0x68, 0x61,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0x79, 0x0a, 0x05, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0xce, 0x02, 0x0a, 0x0e, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x62, 0x75, 0x67,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64,
	0x65, 0x62, 0x75, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x69, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x73, 0x43, 0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x43, 0x61, 0x63, 0x68, 0x65, 0x64, 0x22, 0x60, 0x0a, 0x14, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x22, 0x64, 0x0a, 0x12, 0x43, 0x61, 0x6c,
	0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x69,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x22,
	0x4c, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72,
	0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x8c, 0x01,
	0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61,
	0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x74, 0x79, 0x41, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x70, 0x61, 0x72, 0x74, 0x79, 0x41, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x2f, 0x0a, 0x13,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x56, 0x0a,
	0x1c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x69, 0x65, 0x73, 0x22, 0x2e, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x68, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x03, 0x68, 0x69, 0x74, 0x22, 0x94, 0x01, 0x0a, 0x28, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x66, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x22, 0x28, 0x0a, 0x26,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x32, 0xfe, 0x03, 0x0a, 0x05, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x49, 0x0a, 0x09, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x2e,
	0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x6c,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61,
	0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x30, 0x01, 0x12, 0x53, 0x0a, 0x0d, 0x43,
	0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x61,
	0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1f, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61,
	0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x56, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x22, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x6b, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x12, 0x29, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61,
	0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x8f, 0x01, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x35, 0x2e, 0x61, 0x69,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x6e, 0x64, 0x53, 0x61, 0x76, 0x65, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e,
	0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x3b,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_agent_proto_rawDescOnce sync.Once
	file_agent_agent_proto_rawDescData = file_agent_agent_proto_rawDesc
)

func file_agent_agent_proto_rawDescGZIP() []byte {
	file_agent_agent_proto_rawDescOnce.Do(func() {
		file_agent_agent_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_agent_proto_rawDescData)
	})
	return file_agent_agent_proto_rawDescData
}

var file_agent_agent_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_agent_agent_proto_goTypes = []any{
	(*ChatPayload)(nil),                              // 0: aiapi.agent.ChatPayload
	(*CallAgentRequest)(nil),                         // 1: aiapi.agent.CallAgentRequest
	(*Usage)(nil),                                    // 2: aiapi.agent.Usage
	(*CallAgentReply)(nil),                           // 3: aiapi.agent.CallAgentReply
	(*CallAgentTestRequest)(nil),                     // 4: aiapi.agent.CallAgentTestRequest
	(*CallAgentTestReply)(nil),                       // 5: aiapi.agent.CallAgentTestReply
	(*CustomRule)(nil),                               // 6: aiapi.agent.CustomRule
	(*ContractReviewRequest)(nil),                    // 7: aiapi.agent.ContractReviewRequest
	(*ContractReviewReply)(nil),                      // 8: aiapi.agent.ContractReviewReply
	(*QuestionSecurityCheckRequest)(nil),             // 9: aiapi.agent.QuestionSecurityCheckRequest
	(*QuestionSecurityCheckReply)(nil),               // 10: aiapi.agent.QuestionSecurityCheckReply
	(*CheckAndSaveQuestionSemanticCacheRequest)(nil), // 11: aiapi.agent.CheckAndSaveQuestionSemanticCacheRequest
	(*CheckAndSaveQuestionSemanticCacheReply)(nil),   // 12: aiapi.agent.CheckAndSaveQuestionSemanticCacheReply
}
var file_agent_agent_proto_depIdxs = []int32{
	0,  // 0: aiapi.agent.CallAgentReply.payloads:type_name -> aiapi.agent.ChatPayload
	2,  // 1: aiapi.agent.CallAgentReply.usage:type_name -> aiapi.agent.Usage
	0,  // 2: aiapi.agent.CallAgentTestReply.payloads:type_name -> aiapi.agent.ChatPayload
	6,  // 3: aiapi.agent.ContractReviewRequest.customRules:type_name -> aiapi.agent.CustomRule
	1,  // 4: aiapi.agent.Agent.CallAgent:input_type -> aiapi.agent.CallAgentRequest
	4,  // 5: aiapi.agent.Agent.CallAgentTest:input_type -> aiapi.agent.CallAgentTestRequest
	7,  // 6: aiapi.agent.Agent.ContractReview:input_type -> aiapi.agent.ContractReviewRequest
	9,  // 7: aiapi.agent.Agent.QuestionSecurityCheck:input_type -> aiapi.agent.QuestionSecurityCheckRequest
	11, // 8: aiapi.agent.Agent.CheckAndSaveQuestionSemanticCache:input_type -> aiapi.agent.CheckAndSaveQuestionSemanticCacheRequest
	3,  // 9: aiapi.agent.Agent.CallAgent:output_type -> aiapi.agent.CallAgentReply
	5,  // 10: aiapi.agent.Agent.CallAgentTest:output_type -> aiapi.agent.CallAgentTestReply
	8,  // 11: aiapi.agent.Agent.ContractReview:output_type -> aiapi.agent.ContractReviewReply
	10, // 12: aiapi.agent.Agent.QuestionSecurityCheck:output_type -> aiapi.agent.QuestionSecurityCheckReply
	12, // 13: aiapi.agent.Agent.CheckAndSaveQuestionSemanticCache:output_type -> aiapi.agent.CheckAndSaveQuestionSemanticCacheReply
	9,  // [9:14] is the sub-list for method output_type
	4,  // [4:9] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_agent_agent_proto_init() }
func file_agent_agent_proto_init() {
	if File_agent_agent_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_agent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_agent_agent_proto_goTypes,
		DependencyIndexes: file_agent_agent_proto_depIdxs,
		MessageInfos:      file_agent_agent_proto_msgTypes,
	}.Build()
	File_agent_agent_proto = out.File
	file_agent_agent_proto_rawDesc = nil
	file_agent_agent_proto_goTypes = nil
	file_agent_agent_proto_depIdxs = nil
}

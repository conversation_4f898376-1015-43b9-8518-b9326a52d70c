# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from aiapi.search import search_pb2 as aiapi_dot_search_dot_search__pb2

GRPC_GENERATED_VERSION = '1.67.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in aiapi/search/search_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class SearchStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.FullTextSearch = channel.unary_unary(
                '/aiapi.search.Search/FullTextSearch',
                request_serializer=aiapi_dot_search_dot_search__pb2.FullTextSearchRequest.SerializeToString,
                response_deserializer=aiapi_dot_search_dot_search__pb2.FullTextSearchReply.FromString,
                _registered_method=True)
        self.QASearch = channel.unary_stream(
                '/aiapi.search.Search/QASearch',
                request_serializer=aiapi_dot_search_dot_search__pb2.QASearchRequest.SerializeToString,
                response_deserializer=aiapi_dot_search_dot_search__pb2.QASearchReply.FromString,
                _registered_method=True)
        self.ChatHistory = channel.unary_unary(
                '/aiapi.search.Search/ChatHistory',
                request_serializer=aiapi_dot_search_dot_search__pb2.ChatHistoryRequest.SerializeToString,
                response_deserializer=aiapi_dot_search_dot_search__pb2.ChatHistoryReply.FromString,
                _registered_method=True)
        self.KnowledgeBaseSearch = channel.unary_unary(
                '/aiapi.search.Search/KnowledgeBaseSearch',
                request_serializer=aiapi_dot_search_dot_search__pb2.KnowledgeBaseSearchRequest.SerializeToString,
                response_deserializer=aiapi_dot_search_dot_search__pb2.KnowledgeBaseSearchReply.FromString,
                _registered_method=True)
        self.DoChunks = channel.unary_unary(
                '/aiapi.search.Search/DoChunks',
                request_serializer=aiapi_dot_search_dot_search__pb2.DoChunksRequest.SerializeToString,
                response_deserializer=aiapi_dot_search_dot_search__pb2.DoChunksReply.FromString,
                _registered_method=True)


class SearchServicer(object):
    """Missing associated documentation comment in .proto file."""

    def FullTextSearch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QASearch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def KnowledgeBaseSearch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DoChunks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SearchServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'FullTextSearch': grpc.unary_unary_rpc_method_handler(
                    servicer.FullTextSearch,
                    request_deserializer=aiapi_dot_search_dot_search__pb2.FullTextSearchRequest.FromString,
                    response_serializer=aiapi_dot_search_dot_search__pb2.FullTextSearchReply.SerializeToString,
            ),
            'QASearch': grpc.unary_stream_rpc_method_handler(
                    servicer.QASearch,
                    request_deserializer=aiapi_dot_search_dot_search__pb2.QASearchRequest.FromString,
                    response_serializer=aiapi_dot_search_dot_search__pb2.QASearchReply.SerializeToString,
            ),
            'ChatHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatHistory,
                    request_deserializer=aiapi_dot_search_dot_search__pb2.ChatHistoryRequest.FromString,
                    response_serializer=aiapi_dot_search_dot_search__pb2.ChatHistoryReply.SerializeToString,
            ),
            'KnowledgeBaseSearch': grpc.unary_unary_rpc_method_handler(
                    servicer.KnowledgeBaseSearch,
                    request_deserializer=aiapi_dot_search_dot_search__pb2.KnowledgeBaseSearchRequest.FromString,
                    response_serializer=aiapi_dot_search_dot_search__pb2.KnowledgeBaseSearchReply.SerializeToString,
            ),
            'DoChunks': grpc.unary_unary_rpc_method_handler(
                    servicer.DoChunks,
                    request_deserializer=aiapi_dot_search_dot_search__pb2.DoChunksRequest.FromString,
                    response_serializer=aiapi_dot_search_dot_search__pb2.DoChunksReply.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'aiapi.search.Search', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('aiapi.search.Search', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class Search(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def FullTextSearch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.search.Search/FullTextSearch',
            aiapi_dot_search_dot_search__pb2.FullTextSearchRequest.SerializeToString,
            aiapi_dot_search_dot_search__pb2.FullTextSearchReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def QASearch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/aiapi.search.Search/QASearch',
            aiapi_dot_search_dot_search__pb2.QASearchRequest.SerializeToString,
            aiapi_dot_search_dot_search__pb2.QASearchReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.search.Search/ChatHistory',
            aiapi_dot_search_dot_search__pb2.ChatHistoryRequest.SerializeToString,
            aiapi_dot_search_dot_search__pb2.ChatHistoryReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def KnowledgeBaseSearch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.search.Search/KnowledgeBaseSearch',
            aiapi_dot_search_dot_search__pb2.KnowledgeBaseSearchRequest.SerializeToString,
            aiapi_dot_search_dot_search__pb2.KnowledgeBaseSearchReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DoChunks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.search.Search/DoChunks',
            aiapi_dot_search_dot_search__pb2.DoChunksRequest.SerializeToString,
            aiapi_dot_search_dot_search__pb2.DoChunksReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: aiapi/search/search.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'aiapi/search/search.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19\x61iapi/search/search.proto\x12\x0c\x61iapi.search\x1a\x1fgoogle/protobuf/timestamp.proto\"\x96\x02\n\x15\x46ullTextSearchRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x12\n\nsearchType\x18\x02 \x01(\x03\x12\x10\n\x08\x66ileType\x18\x03 \x01(\t\x12\x10\n\x08ownerIDs\x18\x04 \x03(\x03\x12-\n\tstartTime\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x65ndTime\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07pageNum\x18\t \x01(\x03\x12\x10\n\x08pageSize\x18\n \x01(\x03\x12\x11\n\tclassPath\x18\x0b \x01(\t\x12\x0c\n\x04path\x18\x0c \x01(\t\x12\x16\n\x0e\x66ilterSameFile\x18\r \x01(\x08\"\x96\x02\n\tDocuments\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x11\n\tentityTag\x18\x02 \x01(\t\x12\x14\n\x0cpreEntityTag\x18\x03 \x01(\t\x12\x16\n\x0e\x66ileRelationID\x18\x04 \x01(\x03\x12-\n\tupdatedAt\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05title\x18\x06 \x01(\t\x12\x0e\n\x06userID\x18\x07 \x01(\x03\x12\x10\n\x08userName\x18\x08 \x01(\t\x12\x10\n\x08\x66ullPath\x18\t \x01(\t\x12\x10\n\x08tagNames\x18\n \x03(\t\x12\x0c\n\x04size\x18\x0b \x01(\x03\x12\x10\n\x08mimeType\x18\x0c \x01(\t\x12\x16\n\x0e\x63\x61nDoAiProcess\x18\r \x01(\x08\"\x8e\x01\n\x13\x46ullTextSearchReply\x12\r\n\x05query\x18\x01 \x01(\t\x12\x0f\n\x07tsQuery\x18\x06 \x03(\t\x12\x0f\n\x07pageNum\x18\x02 \x01(\x03\x12\x10\n\x08pageSize\x18\x03 \x01(\x03\x12%\n\x04refs\x18\x04 \x03(\x0b\x32\x17.aiapi.search.Documents\x12\r\n\x05total\x18\x05 \x01(\x03\"J\n\x0fQASearchRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x0f\n\x07roundID\x18\x02 \x01(\x03\x12\x17\n\x0f\x66ileRelationIDs\x18\x03 \x03(\x03\"|\n\rQASearchReply\x12\x0f\n\x07roundID\x18\x01 \x01(\x03\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\x03\x12+\n\x08payloads\x18\x04 \x03(\x0b\x32\x19.aiapi.search.ChatPayload\x12\x0c\n\x04type\x18\x05 \x01(\x03\"7\n\x12\x43hatHistoryRequest\x12\x10\n\x08pageSize\x18\x01 \x01(\x03\x12\x0f\n\x07pageNum\x18\x02 \x01(\x03\"\xbb\x01\n\x0b\x43hatHistory\x12\x0e\n\x06userID\x18\x01 \x01(\x03\x12\x10\n\x08tenantID\x18\x02 \x01(\x03\x12\x0f\n\x07roundID\x18\x03 \x01(\x03\x12\r\n\x05query\x18\x04 \x01(\t\x12\x0e\n\x06\x61nswer\x18\x05 \x01(\t\x12+\n\x08payloads\x18\x06 \x03(\x0b\x32\x19.aiapi.search.ChatPayload\x12-\n\tcreatedAt\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xc2\x01\n\x0b\x43hatPayload\x12\x16\n\x0e\x66ileRelationID\x18\x01 \x01(\x03\x12\x11\n\tentityTag\x18\x02 \x01(\t\x12\x14\n\x0cpreEntityTag\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04size\x18\x05 \x01(\x03\x12\x0e\n\x06userID\x18\x06 \x01(\x03\x12\x10\n\x08mimeType\x18\x07 \x01(\t\x12\r\n\x05index\x18\x08 \x01(\x03\x12\x12\n\nchunkIndex\x18\t \x01(\x03\x12\x11\n\tchunkSize\x18\n \x01(\x03\"@\n\x10\x43hatHistoryReply\x12,\n\thistories\x18\x01 \x03(\x0b\x32\x19.aiapi.search.ChatHistory\"n\n\x1aKnowledgeBaseSearchRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x18\n\x10knowledbeBaseIDs\x18\x02 \x03(\x03\x12\x0e\n\x06maxNum\x18\x03 \x01(\x03\x12\x17\n\x0frerankThreshold\x18\x04 \x01(\x02\",\n\x18KnowledgeBaseSearchReply\x12\x10\n\x08\x63ontents\x18\x01 \x03(\t\";\n\x0f\x44oChunksRequest\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x17\n\x0f\x61\x64vancedProcess\x18\x02 \x01(\x08\"4\n\rDoChunksReply\x12#\n\x06\x63hunks\x18\x01 \x03(\x0b\x32\x13.aiapi.search.Chunk\"(\n\x05\x43hunk\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x0e\n\x06images\x18\x02 \x03(\t*$\n\x08QAStatus\x12\x0b\n\x07RUNNING\x10\x00\x12\x0b\n\x07STOPPED\x10\x01*G\n\x0bQAReplyType\x12\x0b\n\x07\x43ONTENT\x10\x00\x12\x14\n\x10\x41\x42STRACT_PROCESS\x10\x01\x12\x15\n\x11TOO_MANY_REQUESTS\x10\x02\x32\xae\x03\n\x06Search\x12X\n\x0e\x46ullTextSearch\x12#.aiapi.search.FullTextSearchRequest\x1a!.aiapi.search.FullTextSearchReply\x12H\n\x08QASearch\x12\x1d.aiapi.search.QASearchRequest\x1a\x1b.aiapi.search.QASearchReply0\x01\x12O\n\x0b\x43hatHistory\x12 .aiapi.search.ChatHistoryRequest\x1a\x1e.aiapi.search.ChatHistoryReply\x12g\n\x13KnowledgeBaseSearch\x12(.aiapi.search.KnowledgeBaseSearchRequest\x1a&.aiapi.search.KnowledgeBaseSearchReply\x12\x46\n\x08\x44oChunks\x12\x1d.aiapi.search.DoChunksRequest\x1a\x1b.aiapi.search.DoChunksReplyB>Z<gitlab.minum.cloud/innovationteam/ai-api/aiapi/search;searchb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'aiapi.search.search_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z<gitlab.minum.cloud/innovationteam/ai-api/aiapi/search;search'
  _globals['_QASTATUS']._serialized_start=1810
  _globals['_QASTATUS']._serialized_end=1846
  _globals['_QAREPLYTYPE']._serialized_start=1848
  _globals['_QAREPLYTYPE']._serialized_end=1919
  _globals['_FULLTEXTSEARCHREQUEST']._serialized_start=77
  _globals['_FULLTEXTSEARCHREQUEST']._serialized_end=355
  _globals['_DOCUMENTS']._serialized_start=358
  _globals['_DOCUMENTS']._serialized_end=636
  _globals['_FULLTEXTSEARCHREPLY']._serialized_start=639
  _globals['_FULLTEXTSEARCHREPLY']._serialized_end=781
  _globals['_QASEARCHREQUEST']._serialized_start=783
  _globals['_QASEARCHREQUEST']._serialized_end=857
  _globals['_QASEARCHREPLY']._serialized_start=859
  _globals['_QASEARCHREPLY']._serialized_end=983
  _globals['_CHATHISTORYREQUEST']._serialized_start=985
  _globals['_CHATHISTORYREQUEST']._serialized_end=1040
  _globals['_CHATHISTORY']._serialized_start=1043
  _globals['_CHATHISTORY']._serialized_end=1230
  _globals['_CHATPAYLOAD']._serialized_start=1233
  _globals['_CHATPAYLOAD']._serialized_end=1427
  _globals['_CHATHISTORYREPLY']._serialized_start=1429
  _globals['_CHATHISTORYREPLY']._serialized_end=1493
  _globals['_KNOWLEDGEBASESEARCHREQUEST']._serialized_start=1495
  _globals['_KNOWLEDGEBASESEARCHREQUEST']._serialized_end=1605
  _globals['_KNOWLEDGEBASESEARCHREPLY']._serialized_start=1607
  _globals['_KNOWLEDGEBASESEARCHREPLY']._serialized_end=1651
  _globals['_DOCHUNKSREQUEST']._serialized_start=1653
  _globals['_DOCHUNKSREQUEST']._serialized_end=1712
  _globals['_DOCHUNKSREPLY']._serialized_start=1714
  _globals['_DOCHUNKSREPLY']._serialized_end=1766
  _globals['_CHUNK']._serialized_start=1768
  _globals['_CHUNK']._serialized_end=1808
  _globals['_SEARCH']._serialized_start=1922
  _globals['_SEARCH']._serialized_end=2352
# @@protoc_insertion_point(module_scope)

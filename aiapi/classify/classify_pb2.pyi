from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class ClassifyRequest(_message.Message):
    __slots__ = ("entityTag", "preEntityTag", "fileRelationID", "existing_categories")
    ENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    PREENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    FILERELATIONID_FIELD_NUMBER: _ClassVar[int]
    EXISTING_CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    entityTag: str
    preEntityTag: str
    fileRelationID: int
    existing_categories: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, entityTag: _Optional[str] = ..., preEntityTag: _Optional[str] = ..., fileRelationID: _Optional[int] = ..., existing_categories: _Optional[_Iterable[str]] = ...) -> None: ...

class ClassifyResponse(_message.Message):
    __slots__ = ("suggestedCategory",)
    SUGGESTEDCATEGORY_FIELD_NUMBER: _ClassVar[int]
    suggestedCategory: str
    def __init__(self, suggestedCategory: _Optional[str] = ...) -> None: ...

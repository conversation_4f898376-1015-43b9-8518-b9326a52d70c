import json

from aiapi.search.search_pb2 import Chat<PERSON>ayload, ChatHistory as CH
from aiapi.agent.agent_pb2 import ChatPayload as AgentChatPayload
from internal.data.chat_history import ChatHistory


def to_chat_history(history: ChatHistory) -> CH:
    ch = CH(
        userID=history.user_id,
        tenantID=history.tenant_id,
        roundID=history.round_id,
        query=history.query,
        answer=history.answer,
        createdAt=history.created_at
    )

    if history.payloads:
        payloads = json.loads(history.payloads)
        ch.payloads = payloads
    return ch


def to_query_infos(payloads):
    if not payloads:
        return None
    infos = ""
    file_payloads = {}
    for payload in payloads:
        content = payload.get('content')
        file_relation_id = payload.get('fileRelationID')
        name = payload.get('name')
        key = (file_relation_id, name)
        if key in file_payloads:
            file_payloads[key].append(content)
        else:
            file_payloads[key] = [content]

    count = 1
    for key, contents in file_payloads.items():
        content_list = ""
        for i, content in enumerate(contents):
            content_list += f'{i + 1}. {content} \n'
        infos += f'{count}. <<<文件名: {key[1]}>>> <<<文件内容: {content_list}>>> \n'
        count += 1
    return infos


def to_chunk_payloads(data_payloads: list[dict]) -> list[ChatPayload]:
    result = []
    for data_payload in data_payloads:
        payload = ChatPayload(
            fileRelationID=data_payload.get("fileRelationID"),
            entityTag=data_payload.get("entityTag"),
            preEntityTag=data_payload.get("preEntityTag"),
            name=data_payload.get("name"),
            size=data_payload.get("size"),
            userID=data_payload.get("userID"),
            mimeType=data_payload.get("mimeType"),
            index=data_payload.get("index"),
            chunkIndex=data_payload.get("chunkIndex"),
            chunkSize=data_payload.get("chunk_size")
        )
        result.append(payload)
    return result

def to_chunk_payloads_for_agent(data_payloads: list[dict]) -> list[ChatPayload]:
    result = []
    for data_payload in data_payloads:
        payload = AgentChatPayload(
            fileRelationID=data_payload.get("fileRelationID"),
            entityTag=data_payload.get("entityTag"),
            preEntityTag=data_payload.get("preEntityTag"),
            name=data_payload.get("name"),
            size=data_payload.get("size"),
            userID=data_payload.get("userID"),
            mimeType=data_payload.get("mimeType"),
            index=data_payload.get("index"),
            chunkIndex=data_payload.get("chunkIndex"),
            chunkSize=data_payload.get("chunk_size"),
            imageKeys=data_payload.get("imageKeys"),
            fullPath=data_payload.get("fullPath", "")
        )
        result.append(payload)
    return result

def to_chunk_payloads_by_file_meta(data_payloads: list[dict]) -> list[ChatPayload]:
    result = []
    for data_payload in data_payloads:
        payload = AgentChatPayload(
            fileRelationID=data_payload.get("file_relation_id"),
            entityTag=data_payload.get("entity_tag"),
            preEntityTag=data_payload.get("pre_entity_tag"),
            name=data_payload.get("name"),
            size=data_payload.get("size"),
            userID=data_payload.get("user_id"),
            mimeType=data_payload.get("mime_type"),
            index=data_payload.get("index"),
            chunkIndex=data_payload.get("chunk_index"),
            chunkSize=data_payload.get("chunk_size"),
            imageKeys=data_payload.get("imageKeys", [])
        )
        result.append(payload)

    return result

def to_chunk_payloads_by_file_meta_with_content(data_payloads: list[dict]) -> list[ChatPayload]:
    result = []
    for data_payload in data_payloads:
        payload = AgentChatPayload(
            fileRelationID=data_payload.get("file_relation_id"),
            entityTag=data_payload.get("entity_tag"),
            preEntityTag=data_payload.get("pre_entity_tag"),
            name=data_payload.get("name"),
            size=data_payload.get("size"),
            userID=data_payload.get("user_id"),
            mimeType=data_payload.get("mime_type"),
            index=data_payload.get("index"),
            chunkIndex=data_payload.get("chunk_index"),
            chunkSize=data_payload.get("chunk_size"),
            imageKeys=data_payload.get("imageKeys", []),
            content=data_payload.get("content")
        )
        result.append(payload)

    return result

def to_chunk_payloads_with_context_for_agent(data_payloads: list[dict]) -> list[ChatPayload]:
    result = []
    for data_payload in data_payloads:
        payload = AgentChatPayload(
            fileRelationID=data_payload.get("fileRelationID"),
            entityTag=data_payload.get("entityTag"),
            preEntityTag=data_payload.get("preEntityTag"),
            name=data_payload.get("name"),
            size=data_payload.get("size"),
            userID=data_payload.get("userID"),
            mimeType=data_payload.get("mimeType"),
            index=data_payload.get("index"),
            chunkIndex=data_payload.get("chunkIndex"),
            chunkSize=data_payload.get("chunk_size"),
            content=data_payload.get("content")
        )
        result.append(payload)
    return result


def to_dict(chunk_payloads: list[ChatPayload]):
    result = []
    for chunk_payload in chunk_payloads:
        payload = {
            "fileRelationID": chunk_payload.fileRelationID,
            "entityTag": chunk_payload.entityTag,
            "preEntityTag": chunk_payload.preEntityTag,
            "name": chunk_payload.name,
            "size": chunk_payload.size,
            "userID": chunk_payload.userID,
            "mimeType": chunk_payload.mimeType,
            "index": chunk_payload.index,
            "chunkIndex": chunk_payload.chunkIndex,
            "chunk_size": chunk_payload.chunkSize
        }
        result.append(payload)
    return result

def highlight_content(content: str, query: str) -> str:
    if not content:
        return content

    default_result = content[:120]

    try:
        index = content.index(query)
    except Exception:
        return default_result
    else:
        if index >= 20:
            start = index - 10
            return content[start:start+120]
        else:
            return default_result



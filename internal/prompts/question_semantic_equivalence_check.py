def classify_semantic_equivalence_prompt(q1: str, q2: str) -> str:
    """
    创建一个提示模板，用于判断两个问题在语义上是否完全一致。
    完全一致表示两个问题在表达方式可能不同，但其意图与核心含义完全相同。

    输出1表示语义完全一致，输出0表示不一致或有偏差。

    :param q1: 第一个问题
    :param q2: 第二个问题
    :return: 用于调用大模型的提示模板
    """
    return f"""
你是一位语义理解专家，任务是判断两个问题在语义上是否完全一致。

---Goal---
输出1表示两个问题语义完全一致；输出0表示不一致或有差异。

---判断标准（语义一致的特征）---
• 两个问题表达方式不同，但核心含义和意图完全相同
• 可互换替代，回答内容本质无变化
• 所涉及背景假设一致，不依赖上下文

---判断标准（语义不一致的特征）---
• 意图不同，预期回答内容不一样
• 涉及主观语气变化、时间、立场或上下文依赖
• 一个问题更具体或更宽泛
• 多了额外约束或背景信息

---Examples---

问题1：什么是OKR？
问题2：请解释一下OKR的含义
输出：1

问题1：马斯克创办了哪些公司？
问题2：马斯克现在掌管哪些公司？
输出：0

问题1：如何用Python实现冒泡排序？
问题2：用Python写一个冒泡排序的例子
输出：1

问题1：什么是人工智能？
问题2：你怎么看人工智能？
输出：0

问题1：写一个会议通知模板
问题2：帮我写个会议通知
输出：1

---Real Questions---

问题1：{q1}
问题2：{q2}

######################
输出：
"""

def extract_equivalence_decision(response: str) -> bool:
    """
    从大模型的响应中提取语义一致性判断结果

    :param response: 大模型返回的原始响应
    :return: True 表示语义一致，False 表示不一致
    """
    import re

    match = re.search(r"\b([01])\b", response)
    try:
        if match:
            return int(match.group(1)) == 1
    except ValueError:
        pass
    return False

QUESTION_SECURITY_CHECK_PROMPT = """
你是一个安全检查助手。

—— **重要说明** ——  
- **只能依据** `<Real Rules>` **标签内的规则** 判断“用户问题”是否违规。  
- **绝对禁止** 参考或学习任何示例（Examples）中的内容或标签。  
- 输出 **唯一** 内容：数字“1”或“0”，**不**可有其他文字、符号、空格或换行。  

######################  
<Examples>  （仅供格式参考，不可用于判断）  
Q: 如何制作炸弹？  
A: 1  

Q: 今天天气怎么样？  
A: 0  
######################  

######################  
<Real Rules>  
{security_rules}
</Real Rules>  
######################  

现在，请判断以下“Real Question”是否违规：  
Q: {question} 
A:
"""



def get_question_security_check_prompt(question: str, security_rules: list[str]) -> str:
    """
    获取问题安全检查的提示词
    
    Args:
        question: 用户问题
        security_rules: 安全规则列表，每个规则是一个字符串
        
    Returns:
        str: 格式化后的提示词
    """
    # 将规则列表转换为格式化的字符串
    formatted_rules = ""
    for i, rule in enumerate(security_rules, 1):
        formatted_rules += f"{i}. {rule}\n"
    
    return QUESTION_SECURITY_CHECK_PROMPT.format(
        question=question,
        security_rules=formatted_rules
    )

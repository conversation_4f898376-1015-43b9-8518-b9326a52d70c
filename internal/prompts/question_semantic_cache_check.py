def classify_cache_prompt(question: str) -> str:
    """
    创建一个提示模板，用于判断一个问题是否需要进行语义缓存
    如果需要缓存则返回1，否则返回0。

    只有以下问题需要缓存：
    • 问法通用无上下文依赖
    • 语义简洁、闭环提问
    • 教程类/指令类问题
    • 公开事实类问题
    • 有明确模板可归类

    以下问题不需要缓存：
    • 明显包含时间敏感词
    • 明确使用第一人称或主观描述
    • 开放性讨论型提问
    • 连续对话类问题
    • 非目标明确问题

    :param question: 待判断的问题内容
    :return: 用于调用大模型的提示模板
    """
    return f"""
你是一位专业的语义缓存决策专家，需要判断给定的问题是否适合进行语义缓存。

---Goal---
输出1表示需要缓存，输出0表示不需要缓存。

---需要缓存的特征---
• 问法通用无上下文依赖
• 语义简洁、闭环提问
• 教程类/指令类问题
• 公开事实类问题
• 有明确模板可归类

---不需要缓存的特征---
• 明显包含时间敏感词
• 明确使用第一人称或主观描述
• 开放性讨论型提问
• 连续对话类问题
• 非目标明确问题

---Examples---

问题：什么是OKR?
输出：1

问题：如何写一封辞职信?
输出：1

问题：用Python写一个冒泡排序
输出：1

问题：马斯克创办了哪些公司?
输出：1

问题：写一个会议通知模板
输出：1

问题：今天的天气如何?
输出：0

问题：我该怎么提离职?
输出：0

问题：你怎么看待AI取代工作?
输出：0

问题：再给我一个例子
输出：0

问题：帮我处理这个内容
输出：0

---Real Question---
问题：{question}

######################
输出："""


def extract_cache_decision(response: str) -> bool:
    """
    从大模型的响应中提取缓存决策结果

    :param response: 大模型返回的原始响应
    :return: 1或0，默认0
    """
    import re

    # 匹配数字1或0
    match = re.search(r"\b([01])\b", response)
    try:
        if match:
            return int(match.group(1)) == 1
    except ValueError:
        pass
    # 默认不缓存
    return False

import json
import queue
import time
from datetime import datetime, timezone


import grpc
from datahelper.logger import get_logger

from aiapi.agent import agent_pb2
from aiapi.agent import agent_pb2_grpc
from aiapi.agent.agent_pb2 import CallAgentReply, CallAgentTestReply
from aiapi.search.search_pb2 import QAReplyType, QASearchReply, QAStatus
from internal.data.cache import TooManyRequests
from internal.data.data import DataRepo
from internal.graph.workflow import WorkflowJson, create_workflow_from_json
from internal.graph.workflow_executor import WorkflowExecutor
from internal.prompts.contract_review import contract_review_prompt
from internal.prompts.question_security_check import get_question_security_check_prompt
from internal.prompts.question_semantic_cache_check import classify_cache_prompt, extract_cache_decision
from internal.prompts.question_semantic_equivalence_check import classify_semantic_equivalence_prompt, extract_equivalence_decision
from internal.utils.session import get_managed_users, parse_session

logger = get_logger(__name__)

# 创建消息队列
message_queue = queue.Queue()


class Agent(agent_pb2_grpc.AgentServicer):
    def __init__(self):
        self.repo = DataRepo()

    async def CallAgent(self, request: agent_pb2.CallAgentRequest, context: grpc.aio.ServicerContext):
        metadata = dict(context.invocation_metadata())

        session = metadata.get("x-md-global-authz-session")
        if not session:
            await context.abort(grpc.StatusCode.PERMISSION_DENIED)

        uid, tenant_id = await parse_session(session, context)

        request_id = time.time_ns()

        user_infos = get_managed_users(session)
        records = user_infos.get('records')
        user_ids = [user.get('userID') for user in records]

        ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(request.agentID).to_dict()

        if request.aiChatItemID and request.aiChatItemID > 0:
            ai_chat_item = self.repo.ai_chat_item_client.get_ai_chat_item_by_id(request.aiChatItemID).to_dict()
            if ai_chat_item['message']:
                # 将aiChatItemID和message存入队列
                message_queue.put({
                    'aiChatItemID': request.aiChatItemID,
                    'message': ai_chat_item['message']
                })

        # 限流检查

        # 获取限流白名单
        white_list = self.repo.cache.get_white_list()
        if f"{uid}" not in white_list:
            try:
                if ai_agent['model_type'] == 1:
                    if len(user_ids) > 1 or user_infos.get('topDeptManager'):
                        self.repo.cache.is_exceeded(request_id)
                    else:
                        self.repo.cache.is_exceeded(request_id, uid)
                elif ai_agent['model_type'] == 2:
                    if len(user_ids) > 1 or user_infos.get('topDeptManager'):
                        self.repo.external_cache.is_exceeded(request_id)
                    else:
                        self.repo.external_cache.is_exceeded(request_id, uid)

            except TooManyRequests:
                await context.write(QASearchReply(
                    roundID=0,
                    content="",
                    status=QAStatus.STOPPED,
                    type=QAReplyType.TOO_MANY_REQUESTS,
                ))
                return
            except Exception as e:                  
                self.repo.cache.try_release(request_id)
                await context.abort(grpc.StatusCode.INTERNAL, f'服务器内部错误: {e}')

        if not user_infos:
            await context.abort(grpc.StatusCode.DATA_LOSS)

        records = user_infos.get('records')
        user_ids = [user.get('userID') for user in records]

        is_multi_round = request.isMultiRound
        if ai_agent['id'] == 0:
            return
        
        if ai_agent["semantic_cache"]:
            # 查询缓存并判断两个问题的语义是否完全一致
            vss = self.repo.vs.search_by_question_semantic_cache(request.query, [ai_agent['id']])
            payloads = [point.payload for point in vss.points]
            if len(payloads) > 0:
                usePayload = payloads[0]
                for p in payloads:
                    if p.get("question") == request.query:
                        usePayload = p
                        break
                question = usePayload.get("question")
                prompt = classify_semantic_equivalence_prompt(request.query, question)
                response = self.repo.llm_client.question_semantic_equivalence_decision_check(prompt)
                hit = extract_equivalence_decision(response)
                if hit:
                    ref_files = usePayload.get("refFiles")
                    answer = usePayload.get("answer")
                    await context.write(CallAgentReply(roundID=1, content="", status=1, cachePayloads=ref_files, type=0, isCached=True))
                    for answer_index in answer:
                        await context.write(CallAgentReply(roundID=1, content=answer_index,  status=1, type=0, isCached=True))
                    return

        schema_dict = json.loads(ai_agent['schema'])
        schemaJson = WorkflowJson(**schema_dict)
        workflow = create_workflow_from_json(schemaJson)
        executor = WorkflowExecutor(workflow, context)

        await executor.run(
            {"question": request.query, "file_relation_ids": request.fileRelationIDs, "agent_id": ai_agent['id'],
             "chat_id": request.chatID, "user_id": uid, "tenant_id": tenant_id, "user_ids": user_ids,
             "is_multi_round": is_multi_round, "test_mode": False, "internet_search": request.internetSearch, "thinking": request.thinking})

    async def CallAgentTest(self, request: agent_pb2.CallAgentTestRequest, context: grpc.aio.ServicerContext):
        ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(request.agentID).to_dict()
        if ai_agent['id'] == 0:
            return CallAgentTestReply()

        schema_dict = json.loads(ai_agent['schema'])
        schemaJson = WorkflowJson(**schema_dict)
        workflow = create_workflow_from_json(schemaJson)
        executor = WorkflowExecutor(workflow, None)

        response, payloads = await executor.run({
            "question": request.query,
            "file_relation_ids": [],  # 测试模式不需要文件
            "agent_id": ai_agent['id'],
            "chat_id": 0,  # 测试模式不需要chat_id
            "user_id": 1,
            "tenant_id": 1,
            "user_ids": [1],
            "is_multi_round": False,  # 测试模式不需要多轮对话
            "test_mode": True,
            "internet_search": False,
            "thinking": False
        })

        return agent_pb2.CallAgentTestReply(content=response, payloads=payloads)

    async def ContractReview(self, request: agent_pb2.ContractReviewRequest, context: grpc.aio.ServicerContext):
        prompt = contract_review_prompt(list(request.customRules), request.partyA)
        contract_review = self.repo.llm_client.contract_review(prompt, request.fileContent)
        return agent_pb2.CallAgentTestReply(content=contract_review)

    async def QuestionSecurityCheck(self, request: agent_pb2.QuestionSecurityCheckRequest, context: grpc.aio.ServicerContext):
        prompt = get_question_security_check_prompt(request.question, request.policies)
        security_check = self.repo.llm_client.question_security_check(prompt)
        # 判断是否包含1
        return agent_pb2.QuestionSecurityCheckReply(hit="1" in security_check)

    async def CheckAndSaveQuestionSemanticCache(self, request: agent_pb2.CheckAndSaveQuestionSemanticCacheRequest, context: grpc.aio.ServicerContext):
        # 检查是否命中缓存
        prompt = classify_cache_prompt(request.question)
        response = self.repo.llm_client.question_semantic_cache_check(prompt)
        hit = extract_cache_decision(response)
        if hit:
            # 命中缓存
            print(f"命中缓存: {request.question}")
            payload = {
                "question": request.question,
                "answer": request.answer,
                "refFiles": request.refFiles,
                "createdAt": datetime.fromtimestamp(time.time(), tz=timezone.utc).isoformat().replace("+00:00", "Z"),
                "agentID": request.agentID
            }
            self.repo.data_ingestion_client.ingest_question_semantic_cache(payload)
        return agent_pb2.CheckAndSaveQuestionSemanticCacheReply()

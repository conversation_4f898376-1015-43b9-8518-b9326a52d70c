import json
import time
from typing import Any, Coroutine

import datahelper.chunk
import grpc
from datahelper.logger import get_logger

from aiapi.search import search_pb2
from aiapi.search import search_pb2_grpc
from aiapi.search.search_pb2 import FullTextSearchReply, \
    QASearchReply, QAStatus, ChatHistoryReply, QAReplyType, DoChunksReply
from config.config import config
from internal.data.cache import TooManyRequests
from internal.data.data import DataRepo
from internal.utils.convert import to_chat_history, to_query_infos, to_chunk_payloads, to_dict
from internal.utils.session import parse_session, get_managed_users

logger = get_logger(__name__)


def _build_query(requests, data_payloads, messages):
    # if len(data_payloads) > 1:
    #     file_names = [payload.get('name') for payload in data_payloads]
    #     logger.info(f'before rerank: {file_names}')
    #     ranks = rerank(requests.query, data_payloads)
    #     file_names = [(rank[0].get('name'), rank[1]) for rank in ranks]
    #     logger.info(f'after rerank: {file_names}')
    #     if ranks:
    #         data_payloads = [rank[0] for rank in ranks]
    infos = to_query_infos(data_payloads)
    if not infos:
        query = requests.query
    else:
        if 0 < len(infos) < 8192:
            # 保持输出不超过输入的长度
            length = len(infos)
        else:
            length = 2000

        query = f'请参考以下文件内容信息: "{infos}", 用不超过 {length} 个字回答以下问题: "{requests.query}, ' \
                f'请不要使用不相关的内容作为回答的依据"'

    messages.append({'role': 'user', 'content': query})

    # logger.info(f'round_id: {requests.roundID}, 原始问题: {requests.query}, prompt: {messages}')
    return data_payloads, messages


class Search(search_pb2_grpc.SearchServicer):
    def __init__(self):
        self.repo = DataRepo()

    async def build_query(self, uid, tenant_id, request_id, requests: search_pb2.QASearchRequest, session,
                          context: grpc.aio.ServicerContext):
        # 初始化 prompt 上下文
        messages = [
            {'role': 'system', 'content': "你是密数万象智能助手, 属于江苏大道云隐科技有限公司的产品"},
        ]

        histories = None

        user_infos = get_managed_users(session)

        if not user_infos:
            await context.abort(grpc.StatusCode.DATA_LOSS)

        records = user_infos.get('records')
        user_ids = [user.get('userID') for user in records]
        user_map = {user.get('userID'): user.get('userName') for user in records}

        # 限流检查
        if len(user_ids) > 1 or user_infos.get('topDeptManager'):
            self.repo.cache.is_exceeded(request_id)
        else:
            self.repo.cache.is_exceeded(request_id, uid)

        if user_infos.get('topDeptManager'):
            user_ids = []

        # 如果是文件对话, 单独新启动一轮
        if requests.fileRelationIDs:
            requests.roundID = time.time_ns()
            data_payloads = await self.repo.multi_file_search(requests.query, requests.fileRelationIDs,
                                                              user_ids, context)
            return _build_query(requests, data_payloads, messages)

        if not requests.roundID:
            requests.roundID = time.time_ns()
        else:
            histories = self.repo.chat_history_client.get_chat_history_by_round_id(uid, tenant_id, requests.roundID)

        multiple = False
        historical_payload = []
        historical_custom_file_relation_ids = []
        chunked_custom_data_payloads = []
        # 获取合适的长度
        if histories:
            tokens = config.llm_avg_token_per_round
            _histories = []
            for history in histories:
                tokens += history.tokens
                if tokens > config.llm_max_token_per_round:
                    break
                _histories.append(history)

            # 如果本轮对话的历史记录小于历史对话单轮拉取的大小, 那么跳过 llm 校验上下文关联性的逻辑直接进行多轮对话
            if len(histories) == config.llm_chat_history_poll_size_per_round:
                if self.repo.llm_client.relation_reference(requests.query, _histories) == 1:
                    multiple = True
            else:
                multiple = True

            if multiple:
                # 构建多轮对话 prompt
                for history in _histories:
                    messages.append({'role': 'user', 'content': history.query})
                    messages.append({'role': 'assistant', 'content': history.answer})
                    payloads = json.loads(history.payloads)
                    for payload in payloads:
                        if payload.get("index") >= 0:
                            historical_payload.append(payload)
                        else:
                            historical_custom_file_relation_ids.append(payload.get("fileRelationID"))

                # 第一轮用户指定文件关联, 第二轮开始未指定的多轮对话场景
                if len(historical_custom_file_relation_ids) > 0:
                    custom_data_payloads = await self.repo.multi_file_search(requests.query,
                                                                             historical_custom_file_relation_ids,
                                                                             user_ids, context)
                    chunked_custom_data_payloads = self.repo.to_chunked_payloads_from_payloads(custom_data_payloads)

                # 非用户指定文件去重、内容提取
                deduplicated_map = {}
                deduplicated_payloads = []
                content_map = self.repo.file_content_client.get_file_content_by_payloads(historical_payload)
                for payload in historical_payload:
                    content = content_map["{}-{}-{}".format(payload.get("index"),
                                                            payload.get("entityTag"), payload.get("preEntityTag"))]
                    payload['content'] = content
                    key = "{}-{}-{}-{}".format(payload.get("fileRelationID"), payload.get("index"),
                                               payload.get("entityTag"), payload.get("preEntityTag"))
                    if key not in deduplicated_map:
                        deduplicated_map[key] = None
                        deduplicated_payloads.append(payload)
                chunked_custom_data_payloads.extend(
                    self.repo.to_chunked_payloads_from_payloads(deduplicated_payloads))
                _, messages = _build_query(requests, chunked_custom_data_payloads, messages)
            else:
                # 开启新一轮对话
                requests.roundID = time.time_ns()

        if self.repo.llm_client.retrieve_on_demand(query=requests.query, messages=messages, multiple=multiple) == 1:
            data_payloads = self.repo.multi_channel_search(round_id=requests.roundID,
                                                           query=requests.query,
                                                           user_ids=user_ids, user_map=user_map)
            return _build_query(requests, data_payloads, messages)
        else:
            messages.append({'role': 'user', 'content': requests.query})
            return [], messages

    async def FullTextSearch(self, request: search_pb2.FullTextSearchRequest, context: grpc.aio.ServicerContext):
        metadata = dict(context.invocation_metadata())

        session = metadata.get("x-md-global-authz-session")
        if not session:
            await context.abort(grpc.StatusCode.PERMISSION_DENIED)

        res = self.repo.full_text_search(request, session)
        return FullTextSearchReply(
            query=res.get('query'),
            tsQuery=res.get('tsQuery'),
            pageNum=res.get('pageNum'),
            pageSize=res.get('pageSize'),
            refs=res.get('refs'),
            total=res.get('total')
        )

    async def QASearch(self, request: search_pb2.QASearchRequest, context: grpc.aio.ServicerContext) -> None:
        metadata = dict(context.invocation_metadata())

        session = metadata.get("x-md-global-authz-session")
        if not session:
            await context.abort(grpc.StatusCode.PERMISSION_DENIED)

        uid, tenant_id = await parse_session(session, context)
        request_id = time.time_ns()

        try:
            data_payloads, messages = await self.build_query(uid, tenant_id, request_id, request, session, context)
        except TooManyRequests:
            await context.write(QASearchReply(
                roundID=0,
                content="",
                status=QAStatus.STOPPED,
                type=QAReplyType.TOO_MANY_REQUESTS,
            ))
            return
        except Exception as e:
            self.repo.cache.try_release(request_id)
            await context.abort(grpc.StatusCode.INTERNAL, f'服务器内部错误: {e}')

        try:
            logger.info(f"生成问题: {messages}")
            response = await self.repo.llm_client.aclient.chat.completions.create(
                model=config.llm_model_name,
                temperature=0,
                messages=messages,
                stream=True,
                max_tokens=8192,
                frequency_penalty=0,
                presence_penalty=0
            )
            # stream response
            total_content = ""
            chunk_payloads = to_chunk_payloads(data_payloads)
            tokens = len(request.query)
            send_payloads = False

            async for chunk in response:
                choice = chunk.choices[0]
                content = choice.delta.content
                reply = QASearchReply(
                    roundID=request.roundID,
                    content=content,
                    status=QAStatus.RUNNING,
                    type=QAReplyType.CONTENT,
                )
                if not send_payloads:
                    reply.payloads.extend(chunk_payloads)
                    send_payloads = True
                total_content += content
                if choice.finish_reason == "stop":
                    reply.status = QAStatus.STOPPED
                else:
                    tokens += 1
                await context.write(reply)

            self.repo.chat_history_client.add_chat_record(uid, tenant_id,
                                                          request.roundID,
                                                          request.query,
                                                          total_content,
                                                          tokens,
                                                          json.dumps(to_dict(chunk_payloads)))
        except Exception as e:
            await context.abort(grpc.StatusCode.INTERNAL, f'get response from llm, err: {e}')
        finally:
            self.repo.cache.try_release(request_id)

    async def ChatHistory(self, request: search_pb2.ChatHistoryRequest, context: grpc.aio.ServicerContext) -> None:
        metadata = dict(context.invocation_metadata())

        session = metadata.get("x-md-global-authz-session")
        if not session:
            await context.abort(grpc.StatusCode.PERMISSION_DENIED)

        uid, tenant_id = await parse_session(session, context)
        histories = self.repo.chat_history_client.get_chat_history(uid, tenant_id,
                                                                   request.pageSize,
                                                                   (request.pageNum - 1) * request.pageSize)
        reply_histories = []
        for history in histories:
            reply_histories.append(to_chat_history(history))

        return ChatHistoryReply(histories=reply_histories)

    async def KnowledgeBaseSearch(self, request: search_pb2.KnowledgeBaseSearchRequest,
                                  context: grpc.aio.ServicerContext) -> None:
        knowledgeBaseIDs = []
        for knowledgeBaseID in request.knowledbeBaseIDs:
            knowledgeBaseIDs.append(knowledgeBaseID)

        max_num = 5
        if request.maxNum > 0:
            max_num = request.maxNum

        rerank_threshold = 0.3
        if request.rerankThreshold > 0:
            rerank_threshold = request.rerankThreshold

        contents = []
        payloads = self.repo.knowledge_base_search(request.query, knowledgeBaseIDs, [], max_num=max_num, rerank_threshold=rerank_threshold)
        for payload in payloads:
            content = payload["content"]
            if content and len(content) > 0:
                contents.append(content)

        return search_pb2.KnowledgeBaseSearchReply(contents=contents)

    async def DoChunks(self, request: search_pb2.DoChunksRequest,
                       context: grpc.aio.ServicerContext) -> DoChunksReply:
        chunks = datahelper.chunk.do_chunks(request.content, 512, advanced_process=request.advancedProcess)
        result = []
        for chunk in chunks:
            result.append(search_pb2.Chunk(content=chunk.content, images=chunk.images))
        return search_pb2.DoChunksReply(chunks=result)

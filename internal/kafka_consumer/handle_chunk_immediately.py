from concurrent.futures import ThreadPoolExecutor
import json
import threading
import time

from kafka import KafkaConsumer, KafkaProducer
from kafka.errors import CommitFailedError
import redis
from config.config import config
from internal.data.llm import LLM
from datahelper.logger import get_logger
from internal.data.data import DataRepo
from internal.prompts.atomic_knowledge import atomic_knowledge_prompt
from internal.prompts.atomic_question import atomic_question_prompt

logger = get_logger(__name__)

def create_chunk_consumer():
    return KafkaConsumer(
        "fileAnalysisImmediately",
        group_id="ai-api-group",
        bootstrap_servers=config.KAFKA_ADDRESSES,
        # 增加消费者配置
        max_poll_interval_ms=1200000,  # 20分钟的处理时间
        max_poll_records=1,  
        enable_auto_commit=True, 
    )

kafka_producer = KafkaProducer(bootstrap_servers=config.KAFKA_ADDRESSES)

class ChunkImmediatelyHandler(threading.Thread):
    def __init__(self):
        super().__init__()
        self.data_repo = DataRepo()
        self.daemon = True
        self.llm_client = LLM()
        self._running = True
        self.key = "chunk_atomic_handle_immediately_key"
        pool = redis.ConnectionPool.from_url("redis://:{}@{}?decode_responses=True".
                                             format(config.redis_password, config.redis_host))
        self.client = redis.Redis(connection_pool=pool)

        self.thread_pool = ThreadPoolExecutor(max_workers=config.KAFKA_HANDLE_CHUNK_IMMEDIATELY_POOL_SIZE)
        self.active_tasks = 0

    def stop(self):
        """停止消费者线程"""
        self._running = False

    def run(self, *args, **kwargs):
        logger.info("Starting ChunkImmediatelyHandler thread for consuming Kafka messages...")
        
        consumer = create_chunk_consumer()
        
        try:
            while self._running:
                try:

                    # 检查线程池状态，如果所有工作线程都在使用，则等待
                    max_workers = self.thread_pool._max_workers
                    if self.active_tasks >= max_workers:
                        logger.info(f"handle_chunk_immediately线程池已满 ({self.active_tasks}/{max_workers})，等待有可用线程...")
                        # 等待直到有可用线程
                        while self.active_tasks >= max_workers and self._running:
                            consumer.commit()
                            time.sleep(3)  
                        logger.info(f"线程池有可用线程，继续处理 ({self.active_tasks}/{max_workers})")

                    messages = consumer.poll(timeout_ms=1000)
                    if not messages:
                        continue

                    for tp, msgs in messages.items():
                        for msg in msgs:
                            try:

                                consumer.commit()

                                old_msg = self.client.get(self.key)
                                if old_msg:
                                    # 添加kafka消息
                                    kafka_producer.send("fileAnalysis", old_msg.encode('utf-8'))
                                    self.client.delete(self.key)
                                self.client.set(self.key, msg.value)

                                # 提交任务到线程池
                                logger.info("提交任务到线程池处理")
                                future = self.thread_pool.submit(self._process_message, msg.value)
                                self.active_tasks += 1
                                def task_done(future):
                                    try:
                                        print("task_done")
                                        future.result()
                                        
                                        self.client.delete(self.key)
                                    except Exception as e:
                                        logger.error(f"线程池任务执行失败: {str(e)}")
                                        logger.exception(e)
                                    finally:
                                        self.active_tasks -= 1  
                                future.add_done_callback(task_done)
                                
                            except CommitFailedError as e:
                                logger.error(f"Commit failed: {str(e)}")
                            except Exception as e:
                                logger.error(f"Error processing message: {str(e)}")
                                logger.exception(e)

                except Exception as e:
                    logger.error(f"Kafka consumer error: {str(e)}")
                    try:
                        consumer.close()
                    except:
                        pass
                    time.sleep(1)
                    consumer = create_chunk_consumer()
        finally:
            # 关闭消费者
            try:
                consumer.close()
            except Exception as e:
                logger.error(f"Error closing consumer: {str(e)}")


    def _process_message(self, msg):
        print(f"Processing message")
        try:
            msg_json = json.loads(msg)

            file_relation_id = 0
            
            for json_item in msg_json:
                class Point:
                    def __init__(self, payload):
                        self.payload = payload
                    
                class VssResult:
                    def __init__(self, points):
                        self.points = points
                        
                vss = VssResult([Point(json_item)])
                payloads = self.data_repo._to_payloads(vss)

                if payloads and len(payloads) > 0:
                    payload = payloads[0]

                    content = payload.get("content")
                    file_relation_id = payload.get("fileRelationID")
                    pre_entity_tag = payload.get("preEntityTag")
                    entity_tag = payload.get("entityTag")
                    chunk_index = payload.get("chunkIndex")
                    chunk_size = payload.get("chunk_size")
                    index = payload.get("index")
                    imageKeys = payload.get("imageKeys", [])
                    knowledge_base_ids = payload.get("knowledgeBaseIDs", [])
                    user_id = payload.get("userID")

                    # 判断atomic_question是否存在
                    atomic_question = self.data_repo.atomic_question_client.get_atomic_question(
                        file_relation_id, chunk_index, chunk_size, index, entity_tag, pre_entity_tag
                    )
                    if atomic_question:
                        logger.info("Atomic question already exists, skipping...")
                        return

                    if content:
                        knowledge_response = self.llm_client.client.chat.completions.create(
                            model=config.llm_model_name,
                            messages=[{"role": "user", "content": atomic_knowledge_prompt(content)}]
                        )
                        self.data_repo.atomic_knowledge_client.add_atomic_knowledge(
                            file_relation_id=file_relation_id,
                            chunk_index=chunk_index,
                            chunk_size=chunk_size,
                            index=index,
                            knowledge=knowledge_response.choices[0].message.content,
                            entity_tag=entity_tag,
                            pre_entity_tag=pre_entity_tag
                        )

                        question_response = self.llm_client.client.chat.completions.create(
                            model=config.llm_model_name,
                            extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                            messages=[{"role": "user", "content": atomic_question_prompt(content)}]
                        )
                        questions = question_response.choices[0].message.content.split("\n")
                        self.data_repo.atomic_question_client.add_atomic_question(
                            file_relation_id=file_relation_id,
                            chunk_index=chunk_index,
                            chunk_size=chunk_size,
                            index=index,
                            question=questions,
                            entity_tag=entity_tag,
                            pre_entity_tag=pre_entity_tag
                        )
                        atomic_question_metadatas = []
                        for question in questions:
                            if question:
                                atomic_question_metadatas.append({
                                    "fileRelationID": file_relation_id,
                                    "entityTag": entity_tag,
                                    "preEntityTag": pre_entity_tag,
                                    "chunkIndex": chunk_index,
                                    "chunkSize": chunk_size,
                                    "index": index,
                                    "atomicQuestion": question,
                                    "knowledgeBaseIDs": knowledge_base_ids,
                                    "imageKeys": imageKeys,
                                    "userID": user_id
                                })

                        self.data_repo.data_ingestion_client.ingest_atomic_question(atomic_question_metadatas)
                    else:
                        logger.warning("No content found in payload")
                else:
                    logger.warning("No valid payload found in message")
                
            self.data_repo.store_client.UpdateFileQARedisStatus(file_relation_id)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode message as JSON: {str(e)}")
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            logger.error(f"Message that caused error: {msg}")





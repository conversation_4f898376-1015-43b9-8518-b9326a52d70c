from abc import ABC, abstractmethod
from typing import Dict, Any, TypedDict, List, Tuple, Optional
import grpc

from internal.data.data import DataRepo
from internal.data.file_content import FileContentSearch
from internal.data.sensitivity_checker import <PERSON>siti<PERSON><PERSON><PERSON><PERSON>
from internal.graph.workflow import Node, Workflow

class WorkflowState(TypedDict):
    """工作流状态定义"""
    # 存储每个节点的输出，格式为 {node_id: {output_name: NodeOutput}}
    node_outputs: Dict[str, Dict[str, Dict[str, Any]]]
    # 当前正在处理的节点ID
    current_node: str
    # 最终输出结果
    final_output: str
    # 智能体ID
    agent_id: int
    # 聊天ID
    chat_id: int
    # 用户ID
    user_id: int
    # 租户ID
    tenant_id: int
    # 可见用户ID数组
    user_ids: List[int]
    # 是否开启多轮对话
    is_multi_round: bool
    # 多轮对话的上下文 问题和答案
    multi_round_context: List[object]
    # 问题
    question: str
    # 最后一个LLM节点是否连接END节点，如果连接则直接流式返回LLM调用结果并且END节点不流式返回
    is_last_llm_node_connect_end_node: bool
    # 测试用
    test_mode: bool
    # payloads
    payloads: List[Dict[str, Any]]
    # 是否开启互联网搜索
    internet_search: bool
    # 是否开启思考
    thinking: bool

class NodeHandler(ABC):
    """节点处理器基类"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        self.node = node
        self.workflow = workflow
        self.context = context
        self.repo = repo
        self.file_content_client = FileContentSearch
        # 初始化敏感度检查器
        self.sensitivity_checker = SensitivityChecker(self.repo.llm_client)
        
    @abstractmethod
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理节点的抽象方法"""
        pass 
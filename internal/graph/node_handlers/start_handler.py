import json
from typing import Dict, Any
import grpc
import logging

from .base_handler import NodeHandler, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, Workflow
from datahelper.logger import get_logger

logger = get_logger(__name__)

class StartNodeHandler(NodeHandler):
    """开始节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)
        
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理开始节点"""
        if state["test_mode"]:
            return state

        # 查询多轮对话历史
        if state["is_multi_round"] and state["chat_id"] > 0:
            ai_chat = self.repo.ai_chat_client.get_ai_chat_by_id(state["chat_id"]).to_dict()
            if ai_chat:
                ai_chat_items = self.repo.ai_chat_item_client.get_ai_chat_items_by_chat_id_last10(ai_chat["id"])
                # 查出所有问题的集体 一个问题接着一个答案，如果问题后面还是问题则答案为空，object_type为1则是问题， object_type为2则是答案
                history_qas = []
                index = 1
                qa = []
                qa.append(index)
                for item in ai_chat_items:
                    item = item.to_dict()
                    ref_file_contents = []
                    
                    # 处理文件引用
                    if item.get("ref_files"):
                        try:
                            # 将数组中的所有元素合并成一个完整的字符串
                            ref_files_str = ''.join(item.get("ref_files"))
                            # 尝试解析完整的 JSON 字符串
                            file_data_list = json.loads(ref_files_str)
                            # 确保解析结果是列表
                            if isinstance(file_data_list, list):
                                ref_file_contents.extend(file_data_list)
                            else:
                                logger.warning(f"文件引用格式不正确，应为列表: {ref_files_str}")
                        except Exception as e:
                            logger.warning(f"处理文件引用时出错: {str(e)}")

                    if item.get("object_type") == 1:  # 问题
                        if len(qa) > 1:
                            if len(qa) == 2:
                                qa.append('')
                            history_qas.append(qa)
                            qa = []
                            index += 1
                            qa.append(index)
                        qa.append(item.get("message"))
                    elif item.get("object_type") == 2:  # 答案
                        if len(qa) > 1:
                            qa.append(item.get("message"))
                            qa.append(ref_file_contents)
                            history_qas.append(qa)
                            qa = []
                            index += 1
                            qa.append(index)
                
                # 查出相关问题
                relation_question_indexs = self.repo.llm_client.find_relation_question_indexs(state["question"], history_qas)
                if relation_question_indexs:
                    # 分割索引字符串
                    indexs = relation_question_indexs.split(",")
                    valid_indexs = []
                    for index in indexs:
                        try:
                            idx = int(index.strip())
                            if 0 < idx <= len(history_qas):
                                valid_indexs.append(idx - 1)
                        except ValueError:
                            continue
                            
                    # 根据有效索引获取相关的历史问答
                    related_qas = []
                    file_refs_exist = set()  # 改用 set 来存储已存在的文件引用
                    tokens = self.repo.config.llm_avg_token_per_round
                    for idx in valid_indexs:
                        if idx >= len(history_qas):
                            continue
                        #tokens
                        tokens += len(history_qas[idx][1])
                        related_qas.append({"role": "user", "content": history_qas[idx][1]})
                        if tokens > self.repo.config.llm_max_token_per_round:
                            break

                        tokens += len(history_qas[idx][2])
                        if tokens > self.repo.config.llm_max_token_per_round:
                            break
                        assistant_content = {"role": "assistant", "content": history_qas[idx][2]}
                        related_qas.append(assistant_content)
                        
                        # 添加文件引用信息
                        if len(history_qas[idx]) > 3 and history_qas[idx][3]:  # 确保存在文件引用元素
                            try:
                                file_refs = []
                                for ref in history_qas[idx][3]:
                                    if isinstance(ref, dict):
                                        title = ref.get('title', '')
                                        entity_tag = ref.get('entityTag', '')
                                        pre_entity_tag = ref.get('preEntityTag', '')
                                        chunk_index = ref.get('chunkIndex', 0)
                                        chunk_size = ref.get('chunkSize', 0)
                                        content = ""

                                        file_ref_key = f"{entity_tag}-{pre_entity_tag}-{chunk_index}-{chunk_size}"
                                        
                                        if file_ref_key in file_refs_exist:
                                            print(f"存在相同的文件引用: {file_ref_key}")
                                            continue

                                        if entity_tag and pre_entity_tag:
                                            payload = self.repo._to_payloads_from_file_infos([ref])
                                            for p in payload:
                                                if p.get('content'):
                                                    content = p.get('content')
                                                    file_refs.append(f"{title}(content:{content})")
                                                    file_refs_exist.add(file_ref_key)  # 使用 add 方法添加到 set 中
                                
                                if file_refs:  # 只有当有文件引用时才添加到上下文中
                                    file_content = {"role": "file", "content": f'参考文件: ' + ' | '.join(file_refs)}
                                    #tokens
                                    tokens += len(file_content)
                                    if tokens > self.repo.config.llm_max_token_per_round:
                                        break
                                    related_qas.append(file_content)
                            except Exception as e:
                                logger.warning(f"处理历史文件引用时出错: {str(e)}")

                    state["multi_round_context"] = related_qas
        return state 
import grpc
from typing import Dict, Any, List

from .base_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, WorkflowState
from internal.data.data import <PERSON>Repo
from internal.graph.workflow import Node, Workflow
from internal.utils.convert import to_chunk_payloads_with_context_for_agent, to_chunk_payloads_for_agent
from aiapi.agent.agent_pb2 import CallAgentReply

class FileSearchHandler(NodeHandler):
    """知识库检索节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)
    
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理文件检索节点，查询20个最关联文件"""

        if not state["test_mode"]:
            await self.context.write(CallAgentReply(type=0, debugContent="检索文件节点开始..."))

        node_data = self.node.data if isinstance(self.node.data, dict) else self.node.data.__dict__
        
        # 获取查询参数
        inputs_data = node_data.get("inputs", {})
        input_params = inputs_data.get("inputParameters", [])

        query = ""
        for param in input_params:
            if param["input"]["value"]["type"] == "ref":
                source_node = param["input"]["value"]["content"]["blockID"]
                output_name = param["input"]["value"]["content"]["name"]
                query = state["node_outputs"][source_node][output_name]["value"]

        agent_id = state["agent_id"]
        ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(agent_id).to_dict()
        knowledge_base_ids = ai_agent["knowledge_base_ids"]

        user_ids = []
        
        payloads = self.repo.knowledge_base_search(query, knowledge_base_ids, user_ids, 200, 0.1, 0.05)
        if not state["test_mode"]:
            for payload in payloads:
                await self.context.write(CallAgentReply(roundID=1, content="", status=1, payloads=to_chunk_payloads_for_agent([payload]), type=0))
        

        context = ""
        if payloads is not None and len(payloads) > 0:
            count = 1
            for payload in payloads:
                name = payload.get("name")
                content = payload.get("content")
                new_context = f"{count}. <<<文件名: {name}>>> <<<文件内容: {content}>>> \n"
                if len(context) + len(new_context) + 5000 <= self.repo.config.llm_max_token_per_round:
                    context += new_context
                else:
                    break
                count += 1

        state["node_outputs"][self.node.id] = {
            "output": {
                "value": context,
                "type": "string"
            }
        }
        
        return state 
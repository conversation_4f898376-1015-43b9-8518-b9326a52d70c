import time
import grpc
from typing import Dict, Any, List

from .base_handler import <PERSON>de<PERSON>and<PERSON>, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, Workflow
from internal.utils.convert import to_chunk_payloads_for_agent, to_query_infos
from aiapi.agent.agent_pb2 import CallAgentReply
from datahelper.logger import get_logger

logger = get_logger(__name__)

class MinumFileSummaryNodeHandler(NodeHandler):
    """密数万象文件摘要提取节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)
    
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理密数万象文件摘要提取节点"""
        try:

            if not state["test_mode"]:
                await self.context.write(CallAgentReply(type=0, debugContent="密数万象文件摘要提取节点开始..."))
            
            node_data = self.node.data if isinstance(self.node.data, dict) else self.node.data.__dict__

            agent_id = state["agent_id"]
            ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(agent_id).to_dict()
            if ai_agent['id'] == 0:
                return state
            
            inputs_data = node_data.get("inputs", {})
            input_params = inputs_data.get("inputParameters", [])

            # 构建输入数据
            inputs = {}
            for param in input_params:
                if param["input"]["value"]["type"] == "ref":
                    source_node = param["input"]["value"]["content"]["blockID"]
                    output_name = param["input"]["value"]["content"]["name"]
                    inputs[param["name"]] = state["node_outputs"][source_node][output_name]["value"]
                else:
                    inputs[param["name"]] = param["input"]["value"]["content"]

            file_relation_ids = inputs.get("file_relation_ids", [])
            if not file_relation_ids:
                return state
            
            question = inputs.get("question", "")
            
            try:
                queries = self.repo.llm_client.extract_keywords(question)
            except Exception:
                logger.error("关键词提取失败, 直接使用原始查询进行搜索")
                queries = [question]

            user_ids = state["user_ids"]
            try:
                payloads, count = self.file_content_client.search_files_with_relation_ids(file_relation_ids, user_ids)
                state["payloads"] = payloads
            except Exception as e:
                logger.error(f"处理原始文件内容失败, 原因: {e}")
                return state

            if count > self.repo.config.llm_max_token_per_round:
                logger.info("文本超长, 进行摘要处理")
                for i, payload in enumerate(payloads):
                    t1 = time.time() 
                    try:
                        content = await self.repo.llm_client.abstract_for_agent(queries, payload, i, payloads, self.context)
                    except Exception as e:
                        logger.error(f"生成摘要失败, 原因: {e}")
                        continue
                    t2 = time.time()
                    payload['content'] = content
                    logger.info(f"摘要完成, file_relation_id: {payload.get('fileRelationID')}, 耗时: {t2 - t1}")
            else:
                if not state["test_mode"]:
                    for payload in payloads:
                        await self.context.write(CallAgentReply(roundID=1, content="", status=1, payloads=to_chunk_payloads_for_agent([payload]), type=0))
            
            file_summary = to_query_infos(payloads)

            # 尝试在向量库里检索
            # if len(file_summary) < self.repo.config.llm_max_token_per_round:
            #     left_token = self.repo.config.llm_max_token_per_round - len(file_summary)
            #     if left_token > 1024:
            #         try:
            #             context = ""
            #             print(f"question: {question} file_relation_ids: {file_relation_ids}")
            #             results = self.repo.direct_file_search(question, file_relation_ids)

            #             if results is not None and len(results) > 0 and not state["test_mode"]:
            #                 count = 1
            #                 for res in results:
            #                     name = res.get("name")
            #                     content = res.get("content")
            #                     context += f"{count}. <<<文件名: {name}>>> <<<文件内容: {content}>>> \n"
                
            #         except Exception as e:
            #             logger.error(f"向量库检索失败, 原因: {e}")

            #         if context:
            #             context = context[:left_token-500]
            #             file_summary = f"<文件摘要>{file_summary}</文件摘要>  <文件片段>{context}</文件片段>"

            state["node_outputs"][self.node.id] = {
                "output": {
                    "value": file_summary,
                    "type": "string"
                }
            }

            if not state["test_mode"]:
                await self.context.write(CallAgentReply(type=0, debugContent=f"密数万象文件摘要提取节点结束，返回结果: {file_summary}"))
            
        except Exception as e:
            logger.error(f"处理文件摘要失败: {str(e)}")
            # 出错时返回空结果
            state["node_outputs"][self.node.id] = {
                "output": {
                    "value": "",
                    "type": "string"
                }
            }
            
        return state 
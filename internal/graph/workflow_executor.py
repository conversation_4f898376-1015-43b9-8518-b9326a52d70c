import asyncio
import json
import time
from typing import Dict, Any, Callable, List, Tu<PERSON>, TypedDict, Optional, AsyncGenerator, Union
import grpc
from langgraph.graph import StateGraph, END
from langchain_core.output_parsers import StrOutputParser
import ast
import inspect

from openai import OpenAI  # 添加这个导入

from aiapi.agent.agent_pb2 import CallAgentReply, CallAgentRequest, ChatPayload
from internal.data.data import AccurateSearchException, DataRepo
from config.config import config
from internal.data.file_content import FileContentSearch
from datahelper.logger import get_logger
from metrics import metrics
from internal.data.grpc import embedding_client
from internal.graph.node_handlers.file_search_handler import FileSearchHandler
from internal.graph.node_handlers.split_chunk_and_rerank_handler import SplitChunkAndRerankHandler
from internal.utils.convert import to_chunk_payloads_by_file_meta, to_chunk_payloads_for_agent, to_query_infos, to_chunk_payloads_with_context_for_agent
from internal.data.sensitivity_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .workflow import (
    LLMModelType,
    Workflow, 
    NodeType, 
    InputValue,
)

from .node_handlers import (
    NodeHandler,
    StartNodeHandler,
    LLMNodeHandler,
    ConditionNodeHandler, 
    EndNodeHandler,
    KBNodeHandler,
    MinumFileSummaryNodeHandler,
    AtomicSearchNodeHandler
)

from .node_handlers.base_handler import WorkflowState
from .node_handlers.utils import potentially_blocking

logger = get_logger(__name__)

END_NODE_ID = "999999"

class NodeOutput(TypedDict):
    """节点输出定义"""
    value: Any
    type: str

class WorkflowState(TypedDict):
    """工作流状态定义"""
    # 存储每个节点的输出，格式为 {node_id: {output_name: NodeOutput}}
    node_outputs: Dict[str, Dict[str, NodeOutput]]
    # 当前正在处理的节点ID
    current_node: str
    # 最终输出结果
    final_output: str
    # 智能体ID
    agent_id: int
    # 聊天ID
    chat_id: int
    # 用户ID
    user_id: int
    # 租户ID
    tenant_id: int
    # 可见用户ID数组
    user_ids: List[int]
    # 是否开启多轮对话
    is_multi_round: bool
    # 多轮对话的上下文 问题和答案
    multi_round_context: List[Tuple[str, str]]
    # 问题
    question: str
    # 最后一个LLM节点是否连接END节点，如果连接则直接流式返回LLM调用结果并且END节点不流式返回
    is_last_llm_node_connect_end_node: bool = False
    # 测试用
    test_mode: bool = False
    # payloads
    payloads: List[ChatPayload] = []
    # 是否启用互联网搜索
    internet_search: bool = False
    # 是否开启思考
    thinking: bool = False

class WorkflowExecutor:
    """工作流执行器"""
    
    def __init__(self, workflow: Workflow, context: grpc.aio.ServicerContext):
        self.workflow = workflow
        self.graph = StateGraph(state_schema=WorkflowState)
        self.repo = DataRepo()
        self.file_content_client = FileContentSearch
        self.context = context
        self.config = config
        self.embedding_client = embedding_client
        # 初始化敏感度检查器
        self.sensitivity_checker = SensitivityChecker(self.repo.llm_client)
    def create_node_handler(self, node_id: str) -> NodeHandler:
        """创建节点处理器"""
        node = self.workflow.get_node_by_id(node_id)
        if not node:
            raise ValueError(f"找不到节点: {node_id}")
            
        if node.type == NodeType.START.value:
            return StartNodeHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.LLM.value:
            return LLMNodeHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.CONDITION.value:
            return ConditionNodeHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.END.value:
            return EndNodeHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.KB.value:
            return KBNodeHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.MINUM_FILE_SUMMARY.value:
            return MinumFileSummaryNodeHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.ATOMIC_SEARCH.value:
            return AtomicSearchNodeHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.FILE_SEARCH.value:
            return FileSearchHandler(node, self.workflow, self.context, self.repo)
        elif node.type == NodeType.SPLIT_CHUNK_AND_RERANK.value:
            return SplitChunkAndRerankHandler(node, self.workflow, self.context, self.repo)
        else:
            raise ValueError(f"不支持的节点类型: {node.type}")

    def should_continue(self, state: WorkflowState) -> str:
        """判断是否继续执行，返回字符串 'true' 或 'false'"""
        return state.get("condition_result", "false")
    
    def build(self) -> StateGraph:
        """构建工作流图"""
        # 添加所有节点
        for node in self.workflow.nodes:
            # 闭包捕获节点ID
            def create_handler_wrapper(node_id: str):
                async def wrapped_handler(state: WorkflowState) -> WorkflowState:
                    # 更新当前节点ID
                    state["current_node"] = node_id
                    # 创建并调用节点处理器
                    handler = self.create_node_handler(node_id)
                    return await handler.handle(state)
                return wrapped_handler

            # 添加节点到图中
            self.graph.add_node(node.id, create_handler_wrapper(node.id))

        # 添加边
        condition_edges = {}  # 存储条件节点的边 {source_node: {condition: target_node}}
        
        # 先收集所有条件边
        for edge in self.workflow.edges:
            if edge.sourcePortID:
                if edge.sourceNodeID not in condition_edges:
                    condition_edges[edge.sourceNodeID] = {}
                condition_edges[edge.sourceNodeID][edge.sourcePortID] = edge.targetNodeID
        
        # 添加条件边
        for source_node, paths in condition_edges.items():
            self.graph.add_conditional_edges(
                source_node,
                self.should_continue,  # 使用类方法
                paths  # 直接使用收集到的路径映射
            )

        # 添加普通边
        for edge in self.workflow.edges:
            if not edge.sourcePortID:
                self.graph.add_edge(edge.sourceNodeID, edge.targetNodeID)

        # 设置开始和结束节点
        start_node = next(node for node in self.workflow.nodes if node.type == NodeType.START.value)
        end_node = next(node for node in self.workflow.nodes if node.type == NodeType.END.value)
        
        self.graph.set_entry_point(start_node.id)
        self.graph.set_finish_point(end_node.id)

        return self.graph

    async def run(self, inputs: Dict[str, Any]):
        """运行工作流，返回流式结果"""
        # 找到开始节点
        start_node = next(node for node in self.workflow.nodes if node.type == NodeType.START.value)
        
        # 初始化状态
        initial_state = {
            "node_outputs": {
                start_node.id: {  # 初始化开始节点的输出
                    "question": {
                        "value": inputs["question"],
                        "type": "string"
                    },
                    "file_relation_ids": {
                        "value": inputs["file_relation_ids"],
                        "type": "array<integer>"
                    }
                }
            },
            "current_node": start_node.id,  # 从开始节点开始
            "final_output": "",  # 初始化最终输出
            "agent_id": inputs["agent_id"],
            "chat_id": inputs["chat_id"],
            "user_id": inputs["user_id"],
            "tenant_id": inputs["tenant_id"],
            "user_ids": inputs["user_ids"],
            "is_multi_round": inputs["is_multi_round"],
            "multi_round_context": [],
            "question": inputs["question"],
            "test_mode": inputs["test_mode"],
            "payloads": [],
            "is_last_llm_node_connect_end_node": False,
            "internet_search": inputs["internet_search"],
            "thinking": inputs["thinking"]
        }
        
        graph = self.build()
        app = graph.compile()
        
        # 执行工作流
        final_state = await app.ainvoke(initial_state, {"recursion_limit": len(self.workflow.nodes) * 2})
        # 流式返回最终结果
        return final_state.get("final_output", ""), final_state.get("payloads", [])
        
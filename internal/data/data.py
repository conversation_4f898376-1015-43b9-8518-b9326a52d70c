import time
from typing import Optional, Any

import grpc
from datahelper.chunk import do_chunks
from datahelper.logger import get_logger
from deidentification_util import Deidentifier
from metrics import metrics

from aiapi.search.search_pb2 import FullTextSearchRequest
from config.config import config
from internal.data.ai_agent import AiAgent
from internal.data.ai_chat import Ai<PERSON>hat
from internal.data.ai_chat_item import AiChatItem
from internal.data.ai_model import AiModel
from internal.data.ai_model_detail import AiModelDetail
from internal.data.atomic_knowledge import AtomicKnowledge
from internal.data.atomic_question import AtomicQuestion
from internal.data.cache import Cache
from internal.data.chat_history import ChatHistory
from internal.data.data_ingestion import DataIngestion
from internal.data.file_content import FileContentSearch
from internal.data.file_meta import FileMeta
from internal.data.llm import LLM
from internal.data.store import Store
from internal.data.vs import VectorSearch
from internal.utils.convert import to_query_infos
from internal.utils.session import get_managed_users

logger = get_logger(__name__)


class AccurateSearchException(Exception):
    def __str__(self):
        return "精确查询失败"


class DataRepo:
    def __init__(self):
        self.vs = VectorSearch()
        self.file_content_client = FileContentSearch
        self.config = config
        self.chat_history_client = ChatHistory()
        self.ai_agent_client = AiAgent()
        self.ai_chat_client = AiChat()
        self.ai_chat_item_client = AiChatItem()
        self.atomic_knowledge_client = AtomicKnowledge()
        self.atomic_question_client = AtomicQuestion()
        self.ai_model_client = AiModel()
        self.ai_model_detail_client = AiModelDetail()
        self.file_meta_client = FileMeta()
        self.llm_client = LLM()
        # 内部模型限流池
        self.cache = Cache(model_type=1)
        # 外部模型限流池
        self.external_cache = Cache(model_type=2)
        self.data_ingestion_client = DataIngestion()
        self.store_client = Store()
        self.deidentifier = Deidentifier(model_path="model")

    def _to_payloads(self, vss, merge=False):
        payloads = [point.payload for point in vss.points]
        content_map = self.file_content_client.get_file_content_by_payloads(payloads)
        for payload in payloads:
            content = content_map["{}-{}-{}".format(payload.get("index"),
                                                    payload.get("entityTag"), payload.get("preEntityTag"))]
            advanced_process = payload.get("advancedProcess", False)
            chunk_size = payload.get("chunk_size")
            if not chunk_size or chunk_size <= 0:
                chunk_size = 1024
            if content:
                chunks = do_chunks(content, chunk_size, advanced_process=advanced_process)
                chunk_index = payload.get("chunkIndex")
                if chunk_index >= len(chunks):
                    continue
                if 0 < chunk_index < len(chunks) - 1:
                    chunk_content = chunks[chunk_index - 1].content
                    chunk_content += chunks[chunk_index].content
                    chunk_content += chunks[chunk_index + 1].content
                elif chunk_index == 0 and chunk_index < len(chunks) - 1:
                    chunk_content = chunks[chunk_index].content
                    chunk_content += chunks[chunk_index + 1].content
                else:
                    chunk_content = chunks[chunk_index].content
                if chunk_content:
                    payload['content'] = chunk_content

        # 考虑到获取的chunk存在交叉重叠的情况，如果同一个文件的多个chunk大小相加大于文件大小，那么直接使用原始文件内容
        if merge and len(payloads) <= 5:
            entity_tag_content_size = {}
            for payload in payloads:
                content_len = len(payload.get("content")) if payload.get("content") else 0
                entityTag = payload.get("entityTag")
                if entityTag in entity_tag_content_size:
                    entity_tag_content_size[entityTag] += content_len
                else:
                    entity_tag_content_size[entityTag] = content_len

            for payload in payloads:
                if not payload.get("content"):
                    continue

                content = content_map[
                    "{}-{}-{}".format(payload.get("index"), payload.get("entityTag"), payload.get("preEntityTag"))]
                if not content:
                    payload['content'] = ""
                    continue

                entityTag = payload.get("entityTag")
                if entity_tag_content_size[entityTag] >= len(content):
                    payload['content'] = content
                    content_map["{}-{}-{}".format(payload.get("index"), payload.get("entityTag"),
                                                  payload.get("preEntityTag"))] = ""

        return [payload for payload in payloads if payload.get('content')]

    def _to_payloads_from_file_infos(self, file_infos):
        payloads = file_infos
        content_map = self.file_content_client.get_file_content_by_payloads(payloads)
        if len(content_map) == 0:
            return []
        for payload in payloads:
            index = payload.get("index")
            entity_tag = payload.get("entityTag")
            pre_entity_tag = payload.get("preEntityTag")
            if index == -1:
                index = 0

            content = content_map["{}-{}-{}".format(index,
                                                    entity_tag, pre_entity_tag)]
            advanced_process = payload.get("advancedProcess", False)
            chunk_size = payload.get("chunk_size")
            chunk_index = payload.get("chunkIndex")

            if not chunk_size or chunk_size <= 0:
                chunk_size = 1024
            if chunk_index == -1:
                chunk_index = 0
                chunk_size = 4096
            if content:
                chunks = do_chunks(content, chunk_size, advanced_process=advanced_process)

                if chunk_index >= len(chunks):
                    continue
                if 0 < chunk_index < len(chunks) - 1:
                    chunk_content = chunks[chunk_index - 1].content
                    chunk_content += chunks[chunk_index].content
                    chunk_content += chunks[chunk_index + 1].content
                elif chunk_index == 0 and chunk_index < len(chunks) - 1:
                    chunk_content = chunks[chunk_index].content
                    chunk_content += chunks[chunk_index + 1].content
                else:
                    chunk_content = chunks[chunk_index].content
                if chunk_content:
                    payload['content'] = chunk_content
        return [payload for payload in payloads if payload.get('content')]

    def to_chunked_payloads_from_payloads(self, payloads):
        for payload in payloads:
            content = payload.get("content")
            advanced_process = payload.get("advancedProcess", False)
            chunk_size = payload.get("chunk_size")
            if not chunk_size or chunk_size <= 0:
                chunk_size = 1024
            if content:
                chunks = do_chunks(content, chunk_size, advanced_process=advanced_process)
                chunk_index = payload.get("chunkIndex")
                if chunk_index >= len(chunks):
                    continue
                if 0 < chunk_index < len(chunks) - 1:
                    chunk_content = chunks[chunk_index - 1].content
                    chunk_content += chunks[chunk_index].content
                    chunk_content += chunks[chunk_index + 1].content
                elif chunk_index == 0 and chunk_index < len(chunks) - 1:
                    chunk_content = chunks[chunk_index].content
                    chunk_content += chunks[chunk_index + 1].content
                else:
                    chunk_content = chunks[chunk_index].content
                if chunk_content:
                    payload['content'] = chunk_content
        return [payload for payload in payloads if payload.get('content')]

    def multi_channel_search(self, round_id, query, user_ids, user_map) -> list[Optional[dict[str, Any]]]:
        try:
            queries = self.llm_client.extract_keywords(query)
        except Exception:
            logger.error("关键词提取失败, 直接使用原始查询进行搜索")
            queries = [query]

        if config.vs_search_with_file_name:
            # 先基于文件名关键词过滤
            vss = self.vs.search(queries, user_ids, score_threshold=0.4, with_keyword_prefetch=True)

            payloads = self._to_payloads(vss)

            # 如果基于文件名关键词过滤的结果数量不满足所需的长度，那么继续基于文本内容进行向量搜索
            if len(payloads) < config.vs_search_limit:
                vss2 = self.vs.search(queries, user_ids, with_keyword_prefetch=False)
                file_relation_ids = [payload.get('fileRelationID') for payload in payloads]
                for point in vss2.points:
                    if point.payload.get("fileRelationID") in file_relation_ids:
                        continue
                    else:
                        if len(vss.points) < config.vs_search_limit:
                            vss.points.append(point)
                        else:
                            break
                payloads = self._to_payloads(vss)
        else:
            vss = self.vs.search(queries, user_ids, with_keyword_prefetch=False)
            payloads = self._to_payloads(vss)

        # if len(payloads) < config.vs_search_limit:
        #     files = self.file_content_client.search_files_with_content_by_rank(queries, user_ids, user_map,
        #                                                                        1, 0)
        #
        #     file_relation_ids = [payload.get('fileRelationID') for payload in payloads]
        #
        #     for file in files:
        #         file_relation_id = file.get('fileRelationID')
        #         if file_relation_id in file_relation_ids:
        #             continue
        #         else:
        #             # 根据 fileRelationID 反查向量数据库, 查找最相似的 chunk
        #             logger.info(f'round_id: {round_id}, 从全文检索中补充到的文件: {file_relation_id}')
        #             vss = self.vs.search_by_file_relation_ids(queries, [file_relation_id])
        #             if len(vss.points) > 0:
        #                 full_text_result_payloads = self._to_payloads(vss)
        #                 payloads.append(full_text_result_payloads[0])
        return payloads

    async def multi_file_search(self, query, file_relation_ids, user_ids, context: grpc.aio.ServicerContext):
        try:
            queries = self.llm_client.extract_keywords(query)
        except Exception:
            logger.error("关键词提取失败, 直接使用原始查询进行搜索")
            queries = [query]

        # 区分上传文件问答与选择文件问答
        try:
            payloads, count = self.file_content_client. \
                search_files_with_relation_ids(file_relation_ids, user_ids)
        except Exception as e:
            logger.error(f"处理原始文件内容失败, 原因: {e}")
            raise AccurateSearchException

        if count > config.llm_max_token_per_round:
            logger.info("文本超长, 进行摘要处理")
            # 文本摘要
            for i, payload in enumerate(payloads):
                t1 = time.time()
                # 生成摘要进度条
                # 按字数生成, 生成字数到 500 时或者提前结束则进度条变成 100 %, 变成 100 % 后如果生成还未结束则维持 100 %
                try:
                    content = await self.llm_client.abstract(queries, payload, i, payloads, context)
                except Exception as e:
                    logger.error(f"生成摘要失败, 原因: {e}")
                    raise AccurateSearchException
                t2 = time.time()
                payload['content'] = content
                logger.info(f"摘要完成, file_relation_id: {payload.get('fileRelationID')}, 耗时: {t2 - t1}")
                logger.info(f"摘要生产长度: {len(content)}")
        return payloads

    def multi_file_search_v2(self, query, file_relation_ids, user_ids):
        payloads, count = self.file_content_client.search_files_with_relation_ids(file_relation_ids, user_ids)

        useful_chunk_content = ""

        if count > config.llm_max_token_per_round:
            total_chunks = []
            total_chunk_infos = []
            for i, payload in enumerate(payloads):
                content = payload.get("content")
                # 分割 1024 chunk， 冗余512
                chunk_size = 1024
                overlap = 512
                chunks = []
                chunk_infos = []
                if content:
                    total_length = len(content)
                    effective_chunk_size = chunk_size - overlap
                    if total_length <= chunk_size:
                        chunks.append(f"file_name: {payload.get('name')}\nfile_content: {content}")
                        chunk_infos.append({"name": payload.get("name")})
                    else:
                        for start in range(0, total_length, effective_chunk_size):
                            end = min(start + chunk_size, total_length)
                            chunk = content[start:end]
                            chunks.append(f"file_name: {payload.get('name')}\nfile_content: {chunk}")
                            chunk_infos.append({"name": payload.get("name")})
                            if end == total_length:
                                break
                total_chunks.extend(chunks)
                total_chunk_infos.extend(chunk_infos)
            top_n = self.llm_client.knowledge_rerank(query, total_chunks)
            for i, index in enumerate(top_n):
                chunk_content = f"({i})<file_name>{total_chunk_infos[index].get('name')}</file_name><file_content>{total_chunks[index]}</file_content>; "
                if len(useful_chunk_content) + len(chunk_content) + 5000 > self.config.llm_max_token_per_round:
                    break
                useful_chunk_content += chunk_content
            if len(top_n) == 0:
                for i, chunk in enumerate(total_chunks):
                    chunk_content = f"({i})<file_name>{total_chunk_infos[i].get('name')}</file_name><file_content>{total_chunks[i]}</file_content>; "
                    if len(useful_chunk_content) + len(chunk_content) + 5000 > self.config.llm_max_token_per_round:
                        break
                    useful_chunk_content += chunk_content
        else:
            useful_chunk_content = to_query_infos(payloads)

        for payload in payloads:
            payload['content'] = ''

        return payloads, useful_chunk_content


    def full_text_search(self, request: FullTextSearchRequest, session) -> dict:
        ts_query = self.file_content_client.get_ts_query(request.query)
        res = dict(query=request.query, tsQuery=ts_query, pageNum=request.pageNum, pageSize=request.pageSize, refs=[])

        user_infos = get_managed_users(session)
        if not user_infos:
            return res

        if request.pageNum < 1:
            request.pageNum = 1
        offset = (request.pageNum - 1) * request.pageSize

        records = user_infos.get('records')
        user_ids = [str(user.get('userID')) for user in records]
        user_map = {user.get('userID'): user.get('userName') for user in records}

        if "'" in request.query:
            request.query.replace("'", "''")

        docs, total = self.file_content_client.search_files(request, user_ids, user_map, user_infos, offset)
        res['total'] = total
        if not docs:
            return res

        res['refs'] = docs
        return res

    def knowledge_base_search(self, query, knowledge_base_ids, user_ids, max_num=5, rerank_threshold=0.3, kb_score_threshold=0.3):
        if knowledge_base_ids is None and user_ids is None:
            return []

        try:
            queries = self.llm_client.extract_keywords(query)
        except Exception:
            logger.error("关键词提取失败, 直接使用原始查询进行搜索")
            queries = [query]

        if queries is None or len(queries) == 0:
            return []

        kb_search_start_time = time.time()
        vs_search_limit = config.vs_search_limit
        if max_num > config.vs_search_limit:
            vs_search_limit = max_num
           
        vss = self.vs.search_by_knowledge_base_ids(queries, knowledge_base_ids, user_ids, kb_score_threshold, vs_search_limit)
        metrics.knowledgebase_search(kb_search_start_time)

        for point in vss.points:
            metrics.knowledgebase_hit(point.score)

        payloads = self._to_payloads(vss, merge=True)

        knowledges = []
        for payload in payloads:
            knowledges.append(f"{payload.get('name')}: {payload.get('content')}")

        res = []
        rerank_start_time = time.time()
        top_n = self.llm_client.knowledge_rerank(query, knowledges, max_num=max_num, threshold=rerank_threshold)
        metrics.rerank(rerank_start_time)
        for i in top_n:
            res.append(payloads[i])

        logger.info(f"knowledge base rerank, query: {query}, kb: {len(knowledges)}, after rerank: {len(top_n)}")

        return res

    def knowledge_base_search_without_rerank(self, query, knowledge_base_ids, user_ids):
        if knowledge_base_ids is None and user_ids is None:
            return []

        try:
            queries = self.llm_client.extract_keywords(query)
        except Exception:
            logger.error("关键词提取失败, 直接使用原始查询进行搜索")
            queries = [query]

        if queries is None or len(queries) == 0:
            return []

        kb_search_start_time = time.time()
        vss = self.vs.search_by_knowledge_base_ids(queries, knowledge_base_ids, user_ids)
        metrics.knowledgebase_search(kb_search_start_time)

        for point in vss.points:
            metrics.knowledgebase_hit(point.score)

        payloads = self._to_payloads(vss, merge=True)

        # 根据文件去重
        file_map = {}
        res = []
        for payload in payloads:
            file_id = payload.get('fileRelationID')
            if file_id not in file_map:
                file_map[file_id] = True
                res.append(payload)
                # 限制最多返回20个文件
                if len(file_map) >= 20:
                    break

        logger.info(f"knowledge base search without rerank, query: {query}, unique files: {len(file_map)}")

        return res


    def direct_file_search(self, query, file_relation_ids):
        if file_relation_ids is None:
            return []

        try:
            queries = self.llm_client.extract_keywords(query)
        except Exception:
            logger.error("关键词提取失败, 直接使用原始查询进行搜索")
            queries = [query]

        kb_search_start_time = time.time()
        vss = self.vs.search_by_file_relation_ids(queries, file_relation_ids)
        metrics.knowledgebase_search(kb_search_start_time)

        for point in vss.points:
            metrics.knowledgebase_hit(point.score)

        payloads = self._to_payloads(vss, merge=True)

        knowledges = []
        for payload in payloads:
            knowledges.append(payload.get('content'))

        res = []
        rerank_start_time = time.time()
        top_n = self.llm_client.knowledge_rerank(query, knowledges)
        metrics.rerank(rerank_start_time)
        for i in top_n:
            res.append(payloads[i])

        return res

    def atomic_search(self, query, knowledge_base_ids, user_ids):
        if knowledge_base_ids is None:
            knowledge_base_ids = []
        # queries = self.llm_client.extract_keywords(query)
        queries = [query]
        vss = self.vs.search_similar_atomic_question(queries, knowledge_base_ids, user_ids)
        payloads = self._to_payloads(vss)
        if len(payloads) == 0:
            return []

        knowledges = []
        for payload in payloads:
            knowledges.append(payload.get('content'))

        res = []
        top_n = self.llm_client.knowledge_rerank(query, knowledges)
        for i in top_n:
            res.append(payloads[i])

        return res

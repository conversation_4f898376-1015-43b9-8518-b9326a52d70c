#!/usr/bin/env python
"""
启动消息队列消费者的脚本
可以作为独立进程运行
"""
import sys
import time
import signal
import os

# 确保可以导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from internal.queue_consumer.message_consumer import start_consumer, stop_consumer
from datahelper.logger import get_logger

logger = get_logger(__name__)

# 全局变量，用于标记程序是否应该继续运行
running = True


def signal_handler(sig, frame):
    """
    信号处理函数，用于处理SIGINT（Ctrl+C）和SIGTERM
    """
    global running
    logger.info(f"收到信号 {sig}，准备关闭消费者...")
    running = False


def main():
    """
    主函数，启动消息队列消费者并保持运行
    """
    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("启动消息队列消费者...")
    
    # 启动消费者
    consumer = start_consumer()
    
    logger.info("消息队列消费者已启动，按Ctrl+C停止")
    
    try:
        # 保持进程运行，直到收到停止信号
        while running:
            time.sleep(1)
    except Exception as e:
        logger.error(f"运行时出错: {e}")
    finally:
        # 停止消费者
        logger.info("停止消息队列消费者...")
        stop_consumer()
        logger.info("消息队列消费者已停止")


if __name__ == "__main__":
    main() 
import threading
import time
import queue
from datahelper.logger import get_logger
from internal.data.data import DataRepo
from internal.prompts.message_classifier import classify_message_prompt, extract_classification_from_response
from config.config import config

logger = get_logger(__name__)

# 导入消息队列的引用
from internal.service.agent import message_queue


class MessageQueueConsumer(threading.Thread):
    """
    消息队列消费者
    用于从agent.py中的消息队列获取并处理消息
    """
    def __init__(self, queue_ref, process_interval=1.0):
        threading.Thread.__init__(self, daemon=True)
        self.queue = queue_ref
        self.process_interval = process_interval
        self.running = True
        self.logger = logger
        self.repo = DataRepo()
    
    def run(self):
        """
        消费者线程的主要运行方法
        """
        self.logger.info("消息队列消费者已启动")
        while self.running:
            try:
                # 从队列获取消息，超时设置为process_interval
                message = self.queue.get(timeout=self.process_interval)
                res = self.process_message(message)

                self.queue.task_done()
            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                self.logger.error(f"处理消息时出错: {e}")
                # 出错时短暂暂停，防止因持续错误导致CPU使用率过高
                time.sleep(1)
    
    def process_message(self, message):
        """
        处理从队列获取的消息
        
        :param message: 消息对象，包含aiChatItemID和message字段
        """
        try:
            self.logger.info(f"处理消息: aiChatItemID={message['aiChatItemID']}, message长度={len(message['message'])}")
            
            # 使用大模型对消息进行分类
            message_content = message['message']
            classification = self.classify_message(message_content)

            # 更新aiChatItem表
            ai_chat_item = self.repo.ai_chat_item_client.get_ai_chat_item_by_id(message['aiChatItemID'])
            ai_chat_item.primary_classification = classification['primary_category']
            ai_chat_item.secondary_classification = classification['secondary_category']
            # 直接传递分类结果参数给update方法
            self.repo.ai_chat_item_client.update_ai_chat_item_classification(
                message['aiChatItemID'], 
                classification['primary_category'], 
                classification['secondary_category']
            )
            
            # 记录分类结果
            self.logger.info(f"消息分类结果: 一级分类={classification['primary_category']}, " +
                            (f"二级分类={classification['secondary_category']}" if classification['secondary_category'] else "无二级分类"))
            
            self.logger.info(f"消息处理完成: aiChatItemID={message['aiChatItemID']}")
        except Exception as e:
            self.logger.error(f"处理消息'{message.get('aiChatItemID', 'unknown')}'时出错: {e}")
    
    def classify_message(self, message_content):
        """
        使用大模型对消息进行分类
        
        :param message_content: 消息内容
        :return: 分类结果字典
        """
        try:
            # 构建分类提示
            prompt = classify_message_prompt(message_content)
            
            # 调用内部模型进行分类，使用配置中的模型名称而非硬编码的"default"
            response = self.repo.llm_client.client.chat.completions.create(
                model=config.llm_model_name,  # 从配置中获取模型名称
                temperature=0,    # 使用低温度以获得更确定的结果
                messages=[
                    {
                        'role': 'system',
                        'content': '你是一位专业的消息分类专家，能够准确分类各类消息。'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )
            
            # 提取分类结果
            content = response.choices[0].message.content
            classification = extract_classification_from_response(content)
            
            return classification
        except Exception as e:
            self.logger.error(f"消息分类出错: {e}")
            # 返回默认分类
            return {
                "primary_category": "工作",
                "secondary_category": "其他"
            }
    
    def stop(self):
        """
        停止消费者线程
        """
        self.logger.info("正在停止消息队列消费者...")
        self.running = False
        self.join()
        self.logger.info("消息队列消费者已停止")


# 单例消费者实例
_consumer_instance = None

def start_consumer():
    """
    启动消息队列消费者
    如果消费者已经启动，则返回已有实例
    """
    global _consumer_instance
    
    if _consumer_instance is None or not _consumer_instance.is_alive():
        logger.info("启动消息队列消费者...")
        _consumer_instance = MessageQueueConsumer(message_queue)
        _consumer_instance.start()
        logger.info("消息队列消费者启动成功")
    else:
        logger.info("消息队列消费者已在运行中")
    
    return _consumer_instance

def stop_consumer():
    """
    停止消息队列消费者
    """
    global _consumer_instance
    
    if _consumer_instance is not None and _consumer_instance.is_alive():
        _consumer_instance.stop()
        _consumer_instance = None
        logger.info("消息队列消费者已关闭")
    else:
        logger.info("没有正在运行的消息队列消费者") 